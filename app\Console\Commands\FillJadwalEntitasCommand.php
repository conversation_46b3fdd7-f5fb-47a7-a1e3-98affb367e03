<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\FillJadwalEntitasSeeder;

class FillJadwalEntitasCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jadwal:fill-entitas 
                            {--dry-run : <PERSON><PERSON><PERSON>an preview tanpa melakukan update}
                            {--force : Paksa update tanpa konfirmasi}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mengisi entitas_id yang kosong di jadwal kerja dan jadwal masal berdasarkan entitas_id karyawan';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Fill Jadwal Entitas Command');
        $this->info('=====================================');
        
        if ($this->option('dry-run')) {
            $this->dryRun();
            return 0;
        }
        
        if (!$this->option('force')) {
            if (!$this->confirm('Apakah Anda yakin ingin mengisi entitas_id untuk semua jadwal yang kosong?')) {
                $this->info('❌ Operasi dibatalkan.');
                return 0;
            }
        }
        
        $this->info('⏳ Memulai proses pengisian entitas_id...');
        
        try {
            $seeder = new FillJadwalEntitasSeeder();
            $seeder->setCommand($this);
            $seeder->run();
            
            $this->newLine();
            $this->info('🎉 Proses selesai dengan sukses!');
            
            return 0;
        } catch (\Exception $e) {
            $this->error('❌ Terjadi kesalahan: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            
            return 1;
        }
    }
    
    /**
     * Tampilkan preview tanpa melakukan update
     */
    private function dryRun(): void
    {
        $this->info('🔍 DRY RUN MODE - Preview tanpa update');
        $this->newLine();
        
        // Preview Schedule
        $schedulesWithoutEntitas = \App\Models\Schedule::whereNull('entitas_id')
            ->with('karyawan')
            ->get();
            
        $this->info("📅 Schedule yang akan diupdate: {$schedulesWithoutEntitas->count()} records");
        
        if ($schedulesWithoutEntitas->count() > 0) {
            $previewSchedules = $schedulesWithoutEntitas->take(5);
            $scheduleData = [];
            
            foreach ($previewSchedules as $schedule) {
                $scheduleData[] = [
                    $schedule->id,
                    $schedule->karyawan?->nama_lengkap ?? 'Unknown',
                    $schedule->karyawan?->entitas?->nama ?? 'No Entitas',
                    $schedule->karyawan?->entitas_id ?? 'NULL'
                ];
            }
            
            $this->table(
                ['Schedule ID', 'Karyawan', 'Entitas', 'Entitas ID'],
                $scheduleData
            );
            
            if ($schedulesWithoutEntitas->count() > 5) {
                $this->info("... dan " . ($schedulesWithoutEntitas->count() - 5) . " records lainnya");
            }
        }
        
        $this->newLine();
        
        // Preview JadwalMasal
        $jadwalMasalWithoutEntitas = \App\Models\JadwalMasal::whereNull('entitas_id')
            ->with('karyawan')
            ->get();
            
        $this->info("📋 JadwalMasal yang akan diupdate: {$jadwalMasalWithoutEntitas->count()} records");
        
        if ($jadwalMasalWithoutEntitas->count() > 0) {
            $previewJadwalMasal = $jadwalMasalWithoutEntitas->take(5);
            $jadwalMasalData = [];
            
            foreach ($previewJadwalMasal as $jadwalMasal) {
                $jadwalMasalData[] = [
                    $jadwalMasal->id,
                    $jadwalMasal->karyawan?->nama_lengkap ?? 'Unknown',
                    $jadwalMasal->karyawan?->entitas?->nama ?? 'No Entitas',
                    $jadwalMasal->karyawan?->entitas_id ?? 'NULL'
                ];
            }
            
            $this->table(
                ['JadwalMasal ID', 'Karyawan', 'Entitas', 'Entitas ID'],
                $jadwalMasalData
            );
            
            if ($jadwalMasalWithoutEntitas->count() > 5) {
                $this->info("... dan " . ($jadwalMasalWithoutEntitas->count() - 5) . " records lainnya");
            }
        }
        
        $this->newLine();
        $this->info('💡 Untuk menjalankan update sebenarnya, gunakan: php artisan jadwal:fill-entitas');
        $this->info('💡 Untuk skip konfirmasi, gunakan: php artisan jadwal:fill-entitas --force');
    }
}

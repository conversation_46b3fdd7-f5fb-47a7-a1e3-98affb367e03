<?php

namespace App\Filament\Karyawan\Pages;

use Filament\Pages\Dashboard as BaseDashboard;
use App\Models\Karyawan;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;

class Dashboard extends BaseDashboard implements HasForms
{
    use InteractsWithForms;

    protected static string $routePath = '/';
    protected static ?string $navigationIcon = 'heroicon-o-home';
    protected static ?string $navigationLabel = 'Dashboard';
    protected static ?int $navigationSort = 1;
    protected ?string $heading = 'Dashboard Utama';
    protected ?string $subheading = 'Selamat datang di sistem manajemen karyawan';
    protected static string $view = 'filament.karyawan.pages.dashboard';

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('attendance_dashboard')
                ->label('Dashboard Absensi')
                ->icon('heroicon-o-finger-print')
                ->color('primary')
                ->url(fn(): string => route('filament.karyawan.pages.absensi-dashboard')),

            Action::make('cuti_izin')
                ->label('Cuti, Izin & Sakit')
                ->icon('heroicon-o-calendar-days')
                ->color('warning')
                ->url(fn(): string => route('filament.karyawan.resources.cuti-izins.index')),

            Action::make('payroll_dashboard')
                ->label('Dashboard Payroll')
                ->icon('heroicon-o-banknotes')
                ->color('success')
                ->url(fn(): string => route('filament.karyawan.pages.payroll-dashboard')),

            Action::make('profile')
                ->label('Profil Saya')
                ->icon('heroicon-o-user')
                ->color('info')
                ->url(fn(): string => route('filament.karyawan.pages.profile')),
        ];
    }

    public function getWidgets(): array
    {
        return [
            \Filament\Widgets\AccountWidget::class,
            \App\Filament\Karyawan\Widgets\KaryawanStats::class,
            \App\Filament\Karyawan\Widgets\CutiQuotaWidget::class,
            \App\Filament\Karyawan\Widgets\CutiIzinOverview::class,
            \App\Filament\Karyawan\Widgets\SopWidget::class,
        ];
    }

    // Widget didaftarkan di KaryawanPanelProvider

    public function mount(): void
    {
        // Check if the logged-in user has a karyawan record
        $user = Auth::user();
        $karyawan = Karyawan::where('id_user', $user->id)->first();

        if (!$karyawan) {
            Notification::make()
                ->title('Akun tidak terhubung dengan data karyawan')
                ->body('Silahkan hubungi administrator untuk mengaitkan akun Anda dengan data karyawan.')
                ->danger()
                ->persistent()
                ->send();

            // Check if user has the correct role
            if ($user->role !== 'karyawan') {
                Notification::make()
                    ->title('Role tidak sesuai')
                    ->body('Akun Anda tidak memiliki role karyawan. Silahkan hubungi administrator.')
                    ->warning()
                    ->persistent()
                    ->send();
            }
        }
    }
}

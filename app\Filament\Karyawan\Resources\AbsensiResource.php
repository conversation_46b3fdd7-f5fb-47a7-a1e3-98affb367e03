<?php

namespace App\Filament\Karyawan\Resources;

use App\Filament\Karyawan\Resources\AbsensiResource\Pages;
use App\Models\Absensi;
use App\Models\Karyawan;
use App\Services\GeofencingService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

use Carbon\Carbon;

class AbsensiResource extends Resource
{
    protected static ?string $model = Absensi::class;

    protected static ?string $navigationIcon = 'heroicon-o-finger-print';
    protected static ?string $navigationLabel = 'Absensi';
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationGroup = 'Jadwal & Absensi';
    protected static ?string $pluralModelLabel = 'Absensi';
    protected static ?string $modelLabel = 'Absensi';

    public static function canAccess(): bool
    {
        $user = Auth::user();
        $karyawan = $user?->karyawan;
        return $karyawan && $karyawan->status_aktif;
    }

    public static function canCreate(): bool
    {
        $user = Auth::user();
        $karyawan = $user?->karyawan;
        return $karyawan && $karyawan->status_aktif;
    }

    public static function form(Form $form): Form
    {
        $user = Auth::user();
        $karyawan = Karyawan::where('id_user', $user->id)->first();
        $today = Carbon::today()->format('Y-m-d');

        // Get today's schedule
        $jadwal = \App\Models\Schedule::where('karyawan_id', $karyawan?->id)
            ->whereDate('tanggal_jadwal', $today)
            ->with('shift')
            ->first();

        // Get current attendance status for split shift
        $attendanceStatus = null;
        $isCheckIn = true;
        $currentPeriode = 1;
        $statusMessage = 'Belum ada jadwal untuk hari ini';

        //

        if ($jadwal && $jadwal->shift && $karyawan) {
            $attendanceStatus = Absensi::getCurrentAttendanceStatus($karyawan->id, $today, $jadwal->shift);
            $isCheckIn = $attendanceStatus['action'] === 'check_in';
            $currentPeriode = $attendanceStatus['periode'];
            $statusMessage = $attendanceStatus['message'];
        }

        return $form
            ->schema([
                Forms\Components\Hidden::make('karyawan_id')
                    ->default($karyawan?->id),

                Forms\Components\Hidden::make('tanggal_absensi')
                    ->default($today),

                // Workplace Location Information
                Forms\Components\Section::make('Informasi Lokasi Kerja')
                    ->description('Informasi lokasi kerja yang diperbolehkan untuk absensi')
                    ->schema([
                        Forms\Components\Placeholder::make('workplace_location_info')
                            ->label('Lokasi Kerja Anda')
                            ->content(function () use ($karyawan) {
                                if (!$karyawan) {
                                    return new \Illuminate\Support\HtmlString('
                                        <div class="p-3 text-red-800 border border-red-200 rounded-lg bg-red-50">
                                            ❌ Data karyawan tidak ditemukan
                                        </div>
                                    ');
                                }

                                $locationInfo = GeofencingService::getAttendanceLocationInfo($karyawan);

                                if (!$locationInfo['has_location']) {
                                    return new \Illuminate\Support\HtmlString('
                                        <div class="p-3 text-yellow-800 border border-yellow-200 rounded-lg bg-yellow-50">
                                            ⚠️ ' . $locationInfo['message'] . '
                                        </div>
                                    ');
                                }

                                // Tentukan icon dan warna berdasarkan source
                                $sourceIcon = $locationInfo['source'] === 'jadwal kerja hari ini' ? '📅' : '👤';
                                $sourceColor = $locationInfo['source'] === 'jadwal kerja hari ini' ? 'green' : 'blue';
                                $sourceText = $locationInfo['source'] === 'jadwal kerja hari ini' ? 'Berdasarkan Jadwal Hari Ini' : 'Berdasarkan Entitas Default';

                                return new \Illuminate\Support\HtmlString('
                                    <div class="p-4 border border-' . $sourceColor . '-200 rounded-lg bg-' . $sourceColor . '-50">
                                        <div class="flex items-start gap-3">
                                            <div class="flex-shrink-0">
                                                <div class="flex items-center justify-center w-10 h-10 font-bold text-white bg-' . $sourceColor . '-500 rounded-full">
                                                    🏢
                                                </div>
                                            </div>
                                            <div class="flex-1">
                                                <div class="flex items-center gap-2 mb-2">
                                                    <h4 class="font-semibold text-' . $sourceColor . '-900">' . $locationInfo['entitas_name'] . '</h4>
                                                    <span class="px-2 py-1 text-xs font-medium text-' . $sourceColor . '-700 bg-' . $sourceColor . '-100 rounded-full">
                                                        ' . $sourceIcon . ' ' . $sourceText . '
                                                    </span>
                                                </div>
                                                <div class="space-y-2 text-sm text-' . $sourceColor . '-800">
                                                    <div class="flex items-center gap-2">
                                                        <span class="font-medium">📍 Koordinat:</span>
                                                        <span class="px-2 py-1 font-mono bg-' . $sourceColor . '-100 rounded">' . $locationInfo['coordinates'] . '</span>
                                                    </div>
                                                    <div class="flex items-center gap-2">
                                                        <span class="font-medium">📏 Radius:</span>
                                                        <span class="font-semibold">' . $locationInfo['radius'] . ' meter</span>
                                                    </div>
                                                    <div class="flex items-center gap-2">
                                                        <span class="font-medium">📍 Alamat:</span>
                                                        <span>' . ($locationInfo['address'] ?? 'Tidak tersedia') . '</span>
                                                    </div>
                                                </div>
                                                <div class="p-2 mt-3 text-xs text-' . $sourceColor . '-700 bg-' . $sourceColor . '-100 rounded">
                                                    💡 <strong>Catatan:</strong> ' . $locationInfo['message'] . '
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ');
                            })
                    ])
                    ->collapsible()
                    ->collapsed(false),

                Forms\Components\Section::make($isCheckIn ? 'Absensi Masuk' : 'Absensi Keluar')
                    ->description(function () use ($statusMessage, $jadwal, $currentPeriode, $isCheckIn) {
                        $baseMessage = $isCheckIn ? 'Silahkan isi form absensi masuk' : 'Silahkan isi form absensi keluar';
                        $periodeName = $currentPeriode == 1 ? 'Periode 1' : 'Periode 2';

                        if ($jadwal && $jadwal->shift && $jadwal->shift->isSplitShift()) {
                            return $baseMessage . " - {$periodeName}. Status: {$statusMessage}";
                        }

                        return $baseMessage . ". Status: {$statusMessage}";
                    })
                    ->schema([
                        Forms\Components\Hidden::make('latitude')
                            ->required()
                            ->rules([
                                'required',
                                'numeric',
                                'between:-90,90',
                                'not_in:0'
                            ])
                            ->validationMessages([
                                'required' => 'Lokasi harus dideteksi untuk melakukan absensi.',
                                'not_in' => 'Lokasi tidak valid. Silakan aktifkan GPS atau gunakan tombol lokasi.',
                            ]),

                        Forms\Components\Hidden::make('longitude')
                            ->required()
                            ->rules([
                                'required',
                                'numeric',
                                'between:-180,180',
                                'not_in:0'
                            ])
                            ->validationMessages([
                                'required' => 'Lokasi harus dideteksi untuk melakukan absensi.',
                                'not_in' => 'Lokasi tidak valid. Silakan aktifkan GPS atau gunakan tombol lokasi.',
                            ]),

                        Forms\Components\Placeholder::make('geolocation_display')
                            ->label('Status Lokasi')
                            ->content(new \Illuminate\Support\HtmlString('
                                <div class="absensi-location-container">
                                    <div id="location-demo" class="p-3 mb-3 text-blue-800 border border-blue-200 rounded-lg bg-blue-50">
                                        Klik tombol untuk mendapatkan lokasi Anda
                                    </div>
                                    <div class="flex gap-2 mb-3">
                                        <button type="button" onclick="getLocation()" class="px-3 py-2 text-sm text-white bg-blue-500 rounded hover:bg-blue-600">
                                            📍 Dapatkan Lokasi
                                        </button>
                                        <button type="button" onclick="useJakarta()" class="hidden px-3 py-2 text-sm text-white bg-green-500 rounded hover:bg-green-600 ">
                                            🏢 Gunakan Jakarta
                                        </button>
                                        <button type="button" onclick="showManualInput()" class="hidden px-3 py-2 text-sm text-white bg-gray-500 rounded hover:bg-gray-600">
                                            ✏️ Input Manual
                                        </button>
                                    </div>
                                    <div id="manual-input-container" style="display: none;" class="p-3 mb-3 border border-gray-200 rounded-lg bg-gray-50">
                                        <div class="mb-2 text-sm font-medium text-gray-700">Input Koordinat Manual</div>
                                        <div class="grid grid-cols-2 gap-2 mb-2">
                                            <input type="number" id="manual-lat" placeholder="Latitude (contoh: -6.200000)" step="any" class="px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500">
                                            <input type="number" id="manual-lng" placeholder="Longitude (contoh: 106.816666)" step="any" class="px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <div class="flex gap-2">
                                            <button type="button" onclick="useManualCoords()" class="px-3 py-1 text-sm text-white bg-green-500 rounded hover:bg-green-600">
                                                ✅ Gunakan Koordinat
                                            </button>
                                            <button type="button" onclick="hideManualInput()" class="px-3 py-1 text-sm text-white bg-gray-500 rounded hover:bg-gray-600">
                                                ❌ Batal
                                            </button>
                                        </div>
                                        <div class="mt-1 text-xs text-gray-500">Masukkan koordinat dalam format desimal (contoh: -6.200000, 106.816666)</div>
                                    </div>
                                </div>
                                <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
                                <script src="' . asset('js/absensi-geolocation.js') . '"></script>
                            '))
                            ->helperText('Sistem akan mencoba mendeteksi lokasi Anda secara otomatis. Jika gagal, gunakan tombol Jakarta atau input manual.'),

                        Forms\Components\Placeholder::make('camera_interface')
                            ->label($isCheckIn ? 'Foto Masuk' : 'Foto Keluar')
                            ->content(new \Illuminate\Support\HtmlString('
                                <div id="camera-interface-container" style="width: 100%; max-width: 500px; margin: 0 auto;">
                                    <!-- Camera Status -->
                                    <div id="camera-status" style="
                                        padding: 12px;
                                        margin-bottom: 16px;
                                        border-radius: 8px;
                                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                        color: white;
                                        text-align: center;
                                        font-size: 14px;
                                        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                                    ">
                                        <div style="font-size: 24px; margin-bottom: 8px;">📸</div>
                                        <div style="font-weight: 600; font-size: 16px; margin-bottom: 4px;">
                                            Foto Absensi dengan Metadata
                                        </div>
                                        <div style="font-size: 14px; opacity: 0.9;">
                                            Siap mengambil foto dengan metadata lokasi dan waktu
                                        </div>
                                    </div>

                                    <!-- Camera Controls -->
                                    <div style="display: flex; gap: 8px; margin-bottom: 16px; justify-content: center; flex-wrap: wrap;">
                                        <button type="button" id="start-camera-btn" style="
                                            padding: 10px 20px;
                                            background: #059669;
                                            color: white;
                                            border: none;
                                            border-radius: 6px;
                                            cursor: pointer;
                                            font-weight: 500;
                                            font-size: 14px;
                                        ">
                                            📷 Buka Kamera
                                        </button>
                                        <button type="button" id="capture-photo-btn" disabled style="
                                            padding: 10px 20px;
                                            background: #dc2626;
                                            color: white;
                                            border: none;
                                            border-radius: 6px;
                                            cursor: pointer;
                                            font-weight: 500;
                                            font-size: 14px;
                                            opacity: 0.5;
                                        ">
                                            📸 Ambil Foto
                                        </button>
                                        <button type="button" id="stop-camera-btn" disabled style="
                                            padding: 10px 20px;
                                            background: #6b7280;
                                            color: white;
                                            border: none;
                                            border-radius: 6px;
                                            cursor: pointer;
                                            font-weight: 500;
                                            font-size: 14px;
                                            opacity: 0.5;
                                        ">
                                            ⏹️ Tutup
                                        </button>
                                    </div>

                                    <!-- Camera Container -->
                                    <div id="camera-container" style="
                                        display: none;
                                        position: relative;
                                        width: 100%;
                                        max-width: 400px;
                                        margin: 0 auto 16px auto;
                                        border-radius: 12px;
                                        overflow: hidden;
                                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                                    ">
                                        <video id="camera-video" autoplay playsinline style="
                                            width: 100%;
                                            height: auto;
                                            display: block;
                                        "></video>

                                        <!-- Live Metadata Overlay -->
                                        <div id="live-metadata" style="
                                            position: absolute;
                                            bottom: 0;
                                            left: 0;
                                            right: 0;
                                            background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
                                            color: white;
                                            padding: 12px;
                                            font-size: 12px;
                                            font-family: monospace;
                                        ">
                                            <div id="live-coordinates">📍 Menunggu lokasi...</div>
                                            <div id="live-datetime">🕐 <span id="current-time"></span></div>
                                        </div>
                                    </div>

                                    <!-- Preview Container -->
                                    <div id="preview-container" style="
                                        display: none;
                                        position: relative;
                                        width: 100%;
                                        max-width: 400px;
                                        margin: 0 auto 16px auto;
                                    ">
                                        <img id="photo-preview" style="
                                            width: 100%;
                                            height: auto;
                                            border-radius: 12px;
                                            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                                        ">

                                        <div style="display: flex; gap: 8px; margin-top: 12px; justify-content: center;">
                                            <button type="button" id="use-photo-btn" style="
                                                padding: 8px 16px;
                                                background: #059669;
                                                color: white;
                                                border: none;
                                                border-radius: 6px;
                                                cursor: pointer;
                                                font-weight: 500;
                                            ">
                                                ✅ Gunakan Foto
                                            </button>
                                            <button type="button" id="retake-photo-btn" style="
                                                padding: 8px 16px;
                                                background: #f59e0b;
                                                color: white;
                                                border: none;
                                                border-radius: 6px;
                                                cursor: pointer;
                                                font-weight: 500;
                                            ">
                                                🔄 Ambil Ulang
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Hidden canvas for photo processing -->
                                    <canvas id="photo-canvas" style="display: none;"></canvas>
                                </div>

                                <script src="' . asset('js/camera-metadata.js') . '"></script>
                            ')),

                        Forms\Components\FileUpload::make('foto_absensi')
                            ->label($isCheckIn ? 'Foto Masuk' : 'Foto Keluar')
                            ->image()
                            ->required()
                            ->extraAttributes(['style' => 'pointer-events: none; opacity: 0.7;'])
                            ->directory($isCheckIn ? 'absensi/masuk' : 'absensi/keluar')
                            ->disk('public')
                            ->maxSize(5120)
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                            // ->helperText('Gunakan kamera di atas')
                            ->placeholder('Gunakan kamera di atas untuk foto absensi')
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('keterangan')
                            ->label('Keterangan')
                            ->placeholder('Tambahkan keterangan jika diperlukan')
                            ->maxLength(1000),
                    ])
                    ->visible(function () use ($attendanceStatus) {
                        // Show form if action is check_in or check_out
                        return $attendanceStatus && in_array($attendanceStatus['action'], ['check_in', 'check_out']);
                    }),

                Forms\Components\Section::make('Informasi')
                    ->schema([
                        Forms\Components\Placeholder::make('info')
                            ->content(function () use ($attendanceStatus, $jadwal) {
                                if (!$attendanceStatus) {
                                    return 'Tidak ada jadwal kerja untuk hari ini.';
                                }

                                if ($attendanceStatus['action'] === 'completed') {
                                    if ($jadwal && $jadwal->shift && $jadwal->shift->isSplitShift()) {
                                        return 'Anda sudah melakukan absensi lengkap untuk kedua periode hari ini.';
                                    }
                                    return 'Anda sudah melakukan absensi masuk dan keluar untuk hari ini.';
                                }

                                if ($attendanceStatus['action'] === 'waiting') {
                                    return $attendanceStatus['message'];
                                }

                                return $attendanceStatus['message'];
                            })
                    ])
                    ->visible(function () use ($attendanceStatus) {
                        // Show info if completed or waiting
                        return $attendanceStatus && in_array($attendanceStatus['action'], ['completed', 'waiting']);
                    }),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tanggal_absensi')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('periode_display')
                    ->label('Periode')
                    ->badge()
                    ->getStateUsing(function ($record) {
                        if (!$record->jadwal || !$record->jadwal->shift) {
                            return 'Regular';
                        }

                        if ($record->jadwal->shift->isSplitShift()) {
                            return $record->periode == 1 ? 'Periode 1' : 'Periode 2';
                        }

                        return 'Regular';
                    })
                    ->color(fn(string $state): string => match ($state) {
                        'Periode 1' => 'success',
                        'Periode 2' => 'warning',
                        'Regular' => 'primary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('waktu_masuk')
                    ->label('Jam Masuk')
                    ->time('H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('waktu_keluar')
                    ->label('Jam Keluar')
                    ->time('H:i')
                    ->placeholder('-')
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => ucfirst($state))
                    ->color(fn(string $state): string => match (strtolower($state)) {
                        'hadir' => 'success',
                        'terlambat' => 'warning',
                        'izin' => 'info',
                        'sakit' => 'info',
                        'cuti' => 'primary',
                        'alpha' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                // menit keterlambatan
                Tables\Columns\TextColumn::make('menit_terlambat')
                    ->label('Menit Terlambat')
                    ->getStateUsing(function ($record) {
                        if (!$record->waktu_masuk || !$record->jadwal) {
                            return 0;
                        }

                        $shift = $record->jadwal->shift;
                        if (!$shift) {
                            return 0;
                        }

                        try {
                            // Parse waktu masuk aktual
                            $waktuMasukAktual = \Carbon\Carbon::parse($record->waktu_masuk);

                            // Handle split shift
                            if ($shift->is_split_shift ?? false) {
                                if (method_exists($shift, 'getCurrentPeriod') && method_exists($shift, 'getWorkPeriods')) {
                                    $currentPeriod = $shift->getCurrentPeriod($waktuMasukAktual->format('H:i:s'));
                                    $periods = $shift->getWorkPeriods();

                                    foreach ($periods as $period) {
                                        if ($period['periode'] == $currentPeriod) {
                                            $shiftStart = \Carbon\Carbon::parse($period['waktu_mulai']);
                                            $toleranceMinutes = $period['toleransi_keterlambatan'] ?? 0;
                                            $allowedEntry = $shiftStart->copy()->addMinutes($toleranceMinutes);

                                            if ($waktuMasukAktual->greaterThan($allowedEntry)) {
                                                return $waktuMasukAktual->diffInMinutes($allowedEntry);
                                            }
                                            return 0;
                                        }
                                    }
                                }
                            } else {
                                // Regular shift
                                $tanggalWaktuMasuk = $waktuMasukAktual->copy()->startOfDay();
                                $waktuMasukShift = $tanggalWaktuMasuk->copy()->setTimeFromTimeString($shift->waktu_mulai->format('H:i:s'));
                                $toleranceMinutes = $shift->toleransi_keterlambatan ?? 0;
                                $allowedEntry = $waktuMasukShift->copy()->addMinutes($toleranceMinutes);

                                if ($waktuMasukAktual->greaterThan($allowedEntry)) {
                                    return $waktuMasukAktual->diffInMinutes($allowedEntry);
                                }
                            }

                            return 0; // Tidak terlambat
                        } catch (\Exception $e) {
                            return 0;
                        }
                    })
                    ->sortable()
                    ->badge()
                    ->color(fn($state) => $state > 0 ? 'danger' : 'success')
                    ->formatStateUsing(fn($state) => (string) $state),

                // estimasi potongan keterlambatan
                Tables\Columns\TextColumn::make('estimasi_potongan')
                    ->label('Estimasi Potongan')
                    ->getStateUsing(function ($record) {
                        if (!$record->waktu_masuk || !$record->jadwal || $record->status !== 'terlambat') {
                            return 0;
                        }

                        $shift = $record->jadwal->shift;
                        if (!$shift) {
                            return 0;
                        }

                        try {
                            // Parse waktu masuk aktual
                            $waktuMasukAktual = \Carbon\Carbon::parse($record->waktu_masuk);
                            $menitTerlambat = 0;

                            // Handle split shift
                            if ($shift->is_split_shift ?? false) {
                                if (method_exists($shift, 'getCurrentPeriod') && method_exists($shift, 'getWorkPeriods')) {
                                    $currentPeriod = $shift->getCurrentPeriod($waktuMasukAktual->format('H:i:s'));
                                    $periods = $shift->getWorkPeriods();

                                    foreach ($periods as $period) {
                                        if ($period['periode'] == $currentPeriod) {
                                            $shiftStart = \Carbon\Carbon::parse($period['waktu_mulai']);
                                            $toleranceMinutes = $period['toleransi_keterlambatan'] ?? 0;
                                            $allowedEntry = $shiftStart->copy()->addMinutes($toleranceMinutes);

                                            if ($waktuMasukAktual->greaterThan($allowedEntry)) {
                                                $menitTerlambat = $waktuMasukAktual->diffInMinutes($allowedEntry);
                                            }
                                            break;
                                        }
                                    }
                                }
                            } else {
                                // Regular shift
                                $tanggalWaktuMasuk = $waktuMasukAktual->copy()->startOfDay();
                                $waktuMasukShift = $tanggalWaktuMasuk->copy()->setTimeFromTimeString($shift->waktu_mulai->format('H:i:s'));
                                $toleranceMinutes = $shift->toleransi_keterlambatan ?? 0;
                                $allowedEntry = $waktuMasukShift->copy()->addMinutes($toleranceMinutes);

                                if ($waktuMasukAktual->greaterThan($allowedEntry)) {
                                    $menitTerlambat = $waktuMasukAktual->diffInMinutes($allowedEntry);
                                }
                            }

                            if ($menitTerlambat > 0) {
                                // Cari aturan keterlambatan yang sesuai
                                $aturanKeterlambatan = \App\Models\AturanKeterlambatan::where('is_active', true)
                                    ->where('menit_dari', '<=', $menitTerlambat)
                                    ->where(function ($query) use ($menitTerlambat) {
                                        $query->whereNull('menit_sampai')
                                            ->orWhere('menit_sampai', '>=', $menitTerlambat);
                                    })
                                    ->orderBy('menit_dari', 'desc')
                                    ->first();

                                if ($aturanKeterlambatan) {
                                    // Hitung potongan berdasarkan jenis denda
                                    $gajiPokok = $record->karyawan->gaji_pokok ?? 0;
                                    return $aturanKeterlambatan->hitungDenda($menitTerlambat, $gajiPokok);
                                }
                            }

                            return 0;
                        } catch (\Exception $e) {
                            return 0;
                        }
                    })
                    ->money('IDR')
                    ->sortable()
                    ->badge()
                    ->color(fn($state) => $state > 0 ? 'warning' : 'success'),

                Tables\Columns\TextColumn::make('foto_masuk')
                    ->label('Foto Masuk')
                    ->formatStateUsing(function ($record) {
                        $photoUrl = $record->foto_masuk ? asset('storage/' . $record->foto_masuk) : null;
                        $metadata = $record->metadata_foto_masuk ?? [];

                        if (!$photoUrl) {
                            return new \Illuminate\Support\HtmlString('<div style="color: #9ca3af; font-size: 12px;">No Photo</div>');
                        }

                        $hasMetadata = !empty($metadata);
                        $indicator = $hasMetadata ? '<div style="position: absolute; top: -4px; right: -4px; width: 12px; height: 12px; background: #10B981; border: 1px solid white; border-radius: 50%;" title="Foto dengan metadata"></div>' : '';

                        return new \Illuminate\Support\HtmlString('
                            <div style="position: relative; display: inline-block;">
                                <img src="' . $photoUrl . '"
                                     style="width: 40px; height: 40px; object-fit: cover; border-radius: 6px; cursor: pointer;"
                                     onclick="showPhotoModal(\'' . $photoUrl . '\', ' . htmlspecialchars(json_encode(\App\Services\PhotoMetadataService::formatMetadataForDisplay($metadata))) . ', \'masuk\')"
                                     title="Klik untuk melihat detail">
                                ' . $indicator . '
                            </div>
                        ');
                    })
                    ->html(),

                Tables\Columns\TextColumn::make('foto_keluar')
                    ->label('Foto Keluar')
                    ->formatStateUsing(function ($record) {
                        $photoUrl = $record->foto_keluar ? asset('storage/' . $record->foto_keluar) : null;
                        $metadata = $record->metadata_foto_keluar ?? [];

                        if (!$photoUrl) {
                            return new \Illuminate\Support\HtmlString('<div style="color: #9ca3af; font-size: 12px;">No Photo</div>');
                        }

                        $hasMetadata = !empty($metadata);
                        $indicator = $hasMetadata ? '<div style="position: absolute; top: -4px; right: -4px; width: 12px; height: 12px; background: #10B981; border: 1px solid white; border-radius: 50%;" title="Foto dengan metadata"></div>' : '';

                        return new \Illuminate\Support\HtmlString('
                            <div style="position: relative; display: inline-block;">
                                <img src="' . $photoUrl . '"
                                     style="width: 40px; height: 40px; object-fit: cover; border-radius: 6px; cursor: pointer;"
                                     onclick="showPhotoModal(\'' . $photoUrl . '\', ' . htmlspecialchars(json_encode(\App\Services\PhotoMetadataService::formatMetadataForDisplay($metadata))) . ', \'keluar\')"
                                     title="Klik untuk melihat detail">
                                ' . $indicator . '
                            </div>
                        ');
                    })
                    ->html(),

                Tables\Columns\IconColumn::make('approved_at')
                    ->label('Disetujui')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-clock')
                    ->trueColor('success')
                    ->falseColor('warning')
                    ->getStateUsing(fn($record): bool => $record->approved_at !== null),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'hadir' => 'Hadir',
                        'terlambat' => 'Terlambat',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                        'cuti' => 'Cuti',
                        'alpha' => 'Alpha',
                    ]),

                Tables\Filters\Filter::make('tanggal_absensi')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_absensi', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_absensi', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('viewLocation')
                    ->label('Lihat Lokasi')
                    ->icon('heroicon-o-map-pin')
                    ->color('info')
                    ->visible(fn(Absensi $record) => $record->latitude_masuk || $record->latitude_keluar)
                    ->modalHeading('Lokasi Absensi Saya')
                    ->modalContent(function (Absensi $record) {
                        $content = '
                        <div style="background: #f0f9ff; padding: 16px; border-radius: 8px; border: 1px solid #0ea5e9; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 12px 0; font-weight: 600; color: #0c4a6e;">📍 Informasi Lokasi Absensi</h4>';

                        if ($record->latitude_masuk && $record->longitude_masuk) {
                            $content .= '
                                <div style="margin-bottom: 12px;">
                                    <strong style="color: #059669;">Lokasi Masuk:</strong><br>
                                    <span style="font-family: monospace; background: #ecfdf5; padding: 4px 8px; border-radius: 4px; font-size: 14px;">
                                        ' . $record->latitude_masuk . ', ' . $record->longitude_masuk . '
                                    </span>
                                    <a href="https://www.google.com/maps?q=' . $record->latitude_masuk . ',' . $record->longitude_masuk . '"
                                       target="_blank"
                                       style="margin-left: 8px; color: #2563eb; text-decoration: underline; font-size: 14px;">
                                        Buka di Google Maps
                                    </a>
                                </div>';
                        }

                        if ($record->latitude_keluar && $record->longitude_keluar) {
                            $content .= '
                                <div style="margin-bottom: 12px;">
                                    <strong style="color: #dc2626;">Lokasi Keluar:</strong><br>
                                    <span style="font-family: monospace; background: #fef2f2; padding: 4px 8px; border-radius: 4px; font-size: 14px;">
                                        ' . $record->latitude_keluar . ', ' . $record->longitude_keluar . '
                                    </span>
                                    <a href="https://www.google.com/maps?q=' . $record->latitude_keluar . ',' . $record->longitude_keluar . '"
                                       target="_blank"
                                       style="margin-left: 8px; color: #2563eb; text-decoration: underline; font-size: 14px;">
                                        Buka di Google Maps
                                    </a>
                                </div>';
                        }

                        $content .= '</div>';

                        // Add iframe maps for better compatibility
                        if ($record->latitude_masuk && $record->longitude_masuk) {
                            $content .= '
                            <div style="margin-bottom: 20px;">
                                <h5 style="margin: 0 0 8px 0; color: #059669; font-weight: 600;">🟢 Lokasi Masuk</h5>
                                <div style="position: relative; width: 100%; height: 300px; border-radius: 8px; overflow: hidden; border: 1px solid #e5e7eb;">
                                    <iframe
                                        src="https://www.openstreetmap.org/export/embed.html?bbox=' . ($record->longitude_masuk - 0.002) . '%2C' . ($record->latitude_masuk - 0.002) . '%2C' . ($record->longitude_masuk + 0.002) . '%2C' . ($record->latitude_masuk + 0.002) . '&amp;layer=mapnik&amp;marker=' . $record->latitude_masuk . '%2C' . $record->longitude_masuk . '"
                                        style="border: 0; width: 100%; height: 100%;"
                                        allowfullscreen>
                                    </iframe>
                                    <div style="position: absolute; bottom: 8px; left: 8px; background: rgba(255,255,255,0.9); padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                        📍 ' . $record->latitude_masuk . ', ' . $record->longitude_masuk . '
                                    </div>
                                    <div style="position: absolute; top: 8px; right: 8px; background: rgba(34, 197, 94, 0.9); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">
                                        Masuk: ' . $record->waktu_masuk->format('H:i') . '
                                    </div>
                                </div>
                                <div style="margin-top: 8px; text-align: center;">
                                    <a href="https://www.google.com/maps?q=' . $record->latitude_masuk . ',' . $record->longitude_masuk . '"
                                       target="_blank"
                                       style="display: inline-block; padding: 8px 16px; background: #059669; color: white; text-decoration: none; border-radius: 6px; font-size: 14px;">
                                        🗺️ Buka di Google Maps
                                    </a>
                                </div>
                            </div>';
                        }

                        if ($record->latitude_keluar && $record->longitude_keluar) {
                            $content .= '
                            <div style="margin-bottom: 20px;">
                                <h5 style="margin: 0 0 8px 0; color: #dc2626; font-weight: 600;">🔴 Lokasi Keluar</h5>
                                <div style="position: relative; width: 100%; height: 300px; border-radius: 8px; overflow: hidden; border: 1px solid #e5e7eb;">
                                    <iframe
                                        src="https://www.openstreetmap.org/export/embed.html?bbox=' . ($record->longitude_keluar - 0.002) . '%2C' . ($record->latitude_keluar - 0.002) . '%2C' . ($record->longitude_keluar + 0.002) . '%2C' . ($record->latitude_keluar + 0.002) . '&amp;layer=mapnik&amp;marker=' . $record->latitude_keluar . '%2C' . $record->longitude_keluar . '"
                                        style="border: 0; width: 100%; height: 100%;"
                                        allowfullscreen>
                                    </iframe>
                                    <div style="position: absolute; bottom: 8px; left: 8px; background: rgba(255,255,255,0.9); padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                        📍 ' . $record->latitude_keluar . ', ' . $record->longitude_keluar . '
                                    </div>
                                    <div style="position: absolute; top: 8px; right: 8px; background: rgba(220, 38, 38, 0.9); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">
                                        Keluar: ' . ($record->waktu_keluar ? $record->waktu_keluar->format('H:i') : '-') . '
                                    </div>
                                </div>
                                <div style="margin-top: 8px; text-align: center;">
                                    <a href="https://www.google.com/maps?q=' . $record->latitude_keluar . ',' . $record->longitude_keluar . '"
                                       target="_blank"
                                       style="display: inline-block; padding: 8px 16px; background: #dc2626; color: white; text-decoration: none; border-radius: 6px; font-size: 14px;">
                                        🗺️ Buka di Google Maps
                                    </a>
                                </div>
                            </div>';
                        }

                        return new \Illuminate\Support\HtmlString($content);
                    })
                    ->modalWidth('5xl'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //
                ]),
            ])
            ->defaultSort('tanggal_absensi', 'desc')
            ->headerActions([
                Tables\Actions\Action::make('photo_modal_script')
                    ->label('')
                    ->action(fn() => null)
                    ->modalContent(new \Illuminate\Support\HtmlString(''))
                    ->visible(false)
                    ->extraAttributes(['style' => 'display: none;'])
                    ->after(function () {
                        return new \Illuminate\Support\HtmlString('
                           <script>
                            function showPhotoModal(photoUrl, metadata, type) {
                                // Create modal
                                const modal = document.createElement("div");
                                modal.style.cssText = `
                                    position: fixed;
                                    top: 0;
                                    left: 0;
                                    width: 100%;
                                    height: 100%;
                                    background: rgba(0,0,0,0.8);
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    z-index: 9999;
                                    padding: 20px;
                                `;

                                modal.innerHTML = `
                                    <div style="
                                        background: white;
                                        border-radius: 12px;
                                        max-width: 500px;
                                        max-height: 90vh;
                                        overflow: auto;
                                        position: relative;
                                    ">
                                        <div style="position: relative;">
                                            <img src="${photoUrl}" style="
                                                width: 100%;
                                                height: auto;
                                                border-radius: 12px 12px 0 0;
                                                display: block;
                                            ">

                                            <!-- Close button -->
                                            <button onclick="this.closest(\'.modal-overlay\').remove()" style="
                                                position: absolute;
                                                top: 12px;
                                                right: 12px;
                                                width: 32px;
                                                height: 32px;
                                                background: rgba(0,0,0,0.6);
                                                color: white;
                                                border: none;
                                                border-radius: 50%;
                                                cursor: pointer;
                                                display: flex;
                                                align-items: center;
                                                justify-content: center;
                                                font-size: 18px;
                                            ">×</button>

                                            <!-- Metadata overlay -->
                                            <div style="
                                                position: absolute;
                                                bottom: 0;
                                                left: 0;
                                                right: 0;
                                                background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
                                                color: white;
                                                padding: 20px;
                                                border-radius: 0 0 12px 12px;
                                            ">
                                                <h3 style="margin: 0 0 12px 0; font-size: 18px; font-weight: 600;">
                                                    📸 Foto ${type.charAt(0).toUpperCase() + type.slice(1)}
                                                </h3>

                                                ${metadata.coordinates ? `
                                                    <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                                        <span style="font-size: 16px;">📍</span>
                                                        <span style="font-weight: 500;">${metadata.coordinates}</span>
                                                    </div>
                                                ` : ""}

                                                ${metadata.datetime ? `
                                                    <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                                        <span style="font-size: 16px;">🕐</span>
                                                        <span>${metadata.datetime}</span>
                                                    </div>
                                                ` : ""}

                                                ${metadata.status_kehadiran ? `
                                                    <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                                        <span style="font-size: 16px;">
                                                            ${metadata.status_kehadiran === "Tepat Waktu" ? "✅" :
                                                              metadata.status_kehadiran === "Telat" ? "⏰" : "ℹ️"}
                                                        </span>
                                                        <span style="
                                                            font-weight: 600;
                                                            color: ${metadata.status_kehadiran === "Tepat Waktu" ? "#10B981" :
                                                                     metadata.status_kehadiran === "Telat" ? "#F59E0B" : "#6B7280"};
                                                            background: rgba(255,255,255,0.2);
                                                            padding: 4px 8px;
                                                            border-radius: 4px;
                                                        ">${metadata.status_kehadiran}</span>
                                                    </div>
                                                ` : ""}

                                                ${metadata.camera ? `
                                                    <div style="margin-top: 12px; font-size: 12px; opacity: 0.8;">
                                                        📱 ${metadata.camera}
                                                    </div>
                                                ` : ""}
                                            </div>
                                        </div>
                                    </div>
                                `;

                                modal.className = "modal-overlay";
                                modal.onclick = (e) => {
                                    if (e.target === modal) {
                                        modal.remove();
                                    }
                                };

                                document.body.appendChild(modal);
                            }
                            </script>
                        ');
                    })
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAbsensis::route('/'),
            'create' => Pages\CreateAbsensi::route('/create'),
            'view' => Pages\ViewAbsensi::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $user = Auth::user();
        $karyawan = Karyawan::where('id_user', $user->id)->first();

        return parent::getEloquentQuery()
            ->with([
                'jadwal.shift', // Load all shift fields including split shift fields
                'karyawan:id,nama_lengkap,nip',
                'approvedBy:id,name'
            ])
            ->where('karyawan_id', $karyawan?->id);
    }
}

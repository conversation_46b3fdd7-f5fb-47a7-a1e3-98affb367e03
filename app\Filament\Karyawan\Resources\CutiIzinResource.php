<?php

namespace App\Filament\Karyawan\Resources;

use App\Filament\Karyawan\Resources\CutiIzinResource\Pages;
use App\Models\CutiIzin;
use App\Models\Karyawan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class CutiIzinResource extends Resource
{
    protected static ?string $model = CutiIzin::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';
    protected static ?string $navigationLabel = 'Cuti, Izin & Sakit';
    protected static ?int $navigationSort = 4;
    protected static ?string $navigationGroup = 'Jadwal & Absensi';
    protected static ?string $pluralModelLabel = 'Cuti, Izin & Sakit';
    protected static ?string $modelLabel = 'Cuti, Izin & Sakit';

    public static function canAccess(): bool
    {
        $user = Auth::user();
        return $user?->karyawan()->exists();
    }

    public static function canCreate(): bool
    {
        return Auth::user()->karyawan()->exists();
    }

    public static function form(Form $form): Form
    {
        $user = Auth::user();
        $karyawan = Karyawan::where('id_user', $user->id)->first();

        return $form
            ->schema([
                Forms\Components\Hidden::make('karyawan_id')
                    ->default($karyawan?->id),

                // Section untuk menampilkan informasi kuota cuti
                Forms\Components\Section::make('📊 Informasi Kuota Cuti')
                    ->description('Status kuota cuti Anda berdasarkan jenis kontrak')
                    ->schema([
                        Forms\Components\Placeholder::make('quota_info')
                            ->label('')
                            ->content(function () {
                                $user = auth()->user();
                                if (!$user || !$user->karyawan) {
                                    return new \Illuminate\Support\HtmlString('
                                        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
                                            <p class="text-sm text-gray-600">Informasi kuota tidak tersedia</p>
                                        </div>
                                    ');
                                }

                                $karyawan = $user->karyawan;
                                $activeContract = $karyawan->riwayatKontrak()
                                    ->where('is_active', 1)
                                    ->orderBy('tgl_mulai', 'desc')
                                    ->first();

                                if (!$activeContract) {
                                    return new \Illuminate\Support\HtmlString('
                                        <div class="p-4 bg-red-50 rounded-lg border border-red-200">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                                <div class="ml-3">
                                                    <h3 class="text-sm font-medium text-red-800">Tidak Ada Kontrak Aktif</h3>
                                                    <p class="text-sm text-red-700 mt-1">Hubungi HRD untuk informasi kontrak Anda</p>
                                                </div>
                                            </div>
                                        </div>
                                    ');
                                }

                                $contractType = $activeContract->jenis_kontrak;

                                // Jika tidak memiliki hak cuti
                                if (in_array($contractType, ['Freelance', 'Probation'])) {
                                    return new \Illuminate\Support\HtmlString('
                                        <div class="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                                <div class="ml-3">
                                                    <h3 class="text-sm font-medium text-yellow-800">Jenis Kontrak: ' . $contractType . '</h3>
                                                    <p class="text-sm text-yellow-700 mt-1">❌ <strong>TIDAK MEMILIKI HAK CUTI</strong></p>
                                                    <p class="text-sm text-yellow-600 mt-1">Anda hanya dapat mengajukan <strong>Izin</strong> atau <strong>Sakit</strong></p>
                                                </div>
                                            </div>
                                        </div>
                                    ');
                                }

                                // Jika memiliki hak cuti (PKWT/PKWTT)
                                $cutiModel = new \App\Models\CutiIzin();
                                $remainingQuota = $cutiModel->getRemainingLeaveQuota($karyawan->id);
                                $usedQuota = 12 - $remainingQuota;

                                // Hitung kuota bulanan untuk PKWT
                                $monthlyInfo = "";
                                if ($contractType === "PKWT") {
                                    $currentMonth = \Carbon\Carbon::now()->month;
                                    $currentYear = \Carbon\Carbon::now()->year;

                                    $usedDaysThisMonth = \App\Models\CutiIzin::where("karyawan_id", $karyawan->id)
                                        ->where("jenis_permohonan", "cuti")
                                        ->where("status", "approved")
                                        ->whereYear("tanggal_mulai", $currentYear)
                                        ->whereMonth("tanggal_mulai", $currentMonth)
                                        ->sum("jumlah_hari");

                                    $remainingMonthly = max(0, 3 - $usedDaysThisMonth);
                                    $monthlyInfo = "
                                        <div class=\"mt-2 pt-2 border-t border-blue-200\">
                                            <p class=\"text-sm text-blue-700\">📅 <strong>Kuota Bulanan:</strong> {$remainingMonthly} hari tersisa (dari 3 hari/bulan)</p>
                                            <p class=\"text-xs text-blue-600 mt-1\">* Batas bulanan hanya sebagai peringatan, tidak memblokir pengajuan</p>
                                        </div>
                                    ";
                                }

                                $statusColor = $remainingQuota > 6 ? "green" : ($remainingQuota > 3 ? "yellow" : "red");
                                $statusIcon = $remainingQuota > 6 ? "✅" : ($remainingQuota > 3 ? "⚠️" : "🚨");

                                return new \Illuminate\Support\HtmlString("
                                    <div class=\"p-4 bg-{$statusColor}-50 rounded-lg border border-{$statusColor}-200\">
                                        <div class=\"flex items-start\">
                                            <div class=\"flex-shrink-0\">
                                                <svg class=\"h-5 w-5 text-{$statusColor}-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">
                                                    <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\" />
                                                </svg>
                                            </div>
                                            <div class=\"ml-3 flex-1\">
                                                <h3 class=\"text-sm font-medium text-{$statusColor}-800\">Jenis Kontrak: {$contractType}</h3>
                                                <div class=\"mt-2 space-y-1\">
                                                    <p class=\"text-sm text-{$statusColor}-700\">{$statusIcon} <strong>Sisa Kuota Tahunan:</strong> {$remainingQuota} hari (dari 12 hari)</p>
                                                    <p class=\"text-sm text-{$statusColor}-700\">📊 <strong>Cuti Terpakai:</strong> {$usedQuota} hari</p>
                                                    <p class=\"text-sm text-{$statusColor}-700\">✅ <strong>Status:</strong> " . ($remainingQuota > 0 ? "BOLEH MENGAJUKAN CUTI" : "KUOTA HABIS") . "</p>
                                                </div>
                                                {$monthlyInfo}
                                            </div>
                                        </div>
                                    </div>
                                ");
                            })
                    ])
                    ->collapsible()
                    ->collapsed(false),

                Forms\Components\Section::make('Informasi Permohonan')
                    ->description('Silahkan isi form permohonan cuti atau izin dengan lengkap')
                    ->schema([
                        Forms\Components\Select::make('jenis_permohonan')
                            ->label('Jenis Permohonan')
                            ->options([
                                'cuti' => 'Cuti',
                                'izin' => 'Izin',
                                'sakit' => 'Sakit',
                            ])
                            ->required()
                            ->placeholder('Pilih jenis permohonan')
                            ->reactive()
                            ->helperText(function () {
                                $user = auth()->user();
                                if (!$user || !$user->karyawan) {
                                    return 'Pilih "Cuti" untuk cuti tahunan/khusus, "Izin" untuk izin sementara, atau "Sakit" untuk cuti sakit';
                                }

                                $karyawan = $user->karyawan;
                                $activeContract = $karyawan->riwayatKontrak()
                                    ->where('is_active', 1)
                                    ->orderBy('tgl_mulai', 'desc')
                                    ->first();

                                if (!$activeContract) {
                                    return 'Pilih "Cuti" untuk cuti tahunan/khusus, "Izin" untuk izin sementara, atau "Sakit" untuk cuti sakit';
                                }

                                $contractType = $activeContract->jenis_kontrak;

                                if (in_array($contractType, ['Freelance', 'Probation'])) {
                                    return "⚠️ Jenis kontrak {$contractType} tidak memiliki hak cuti. Hanya bisa mengajukan izin atau sakit.";
                                }

                                $cutiModel = new \App\Models\CutiIzin();
                                $remainingQuota = $cutiModel->getRemainingLeaveQuota($karyawan->id);

                                return "📋 Sisa kuota cuti tahun ini: {$remainingQuota} hari (dari 12 hari). Pilih jenis permohonan yang sesuai.";
                            }),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('tanggal_mulai')
                                    ->label('Tanggal Mulai')
                                    ->required()
                                    ->minDate(Carbon::today())
                                    ->placeholder('Pilih tanggal mulai')
                                    ->helperText('Tanggal mulai cuti/izin')
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        $tanggalSelesai = $get('tanggal_selesai');
                                        if ($state && $tanggalSelesai) {
                                            $model = new CutiIzin([
                                                'tanggal_mulai' => $state,
                                                'tanggal_selesai' => $tanggalSelesai
                                            ]);
                                            $set('jumlah_hari', $model->calculateJumlahHari());
                                        }
                                    }),

                                Forms\Components\DatePicker::make('tanggal_selesai')
                                    ->label('Tanggal Selesai')
                                    ->required()
                                    ->minDate(fn(callable $get) => $get('tanggal_mulai') ?: Carbon::today())
                                    ->placeholder('Pilih tanggal selesai')
                                    ->helperText('Tanggal selesai cuti/izin')
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        $tanggalMulai = $get('tanggal_mulai');
                                        if ($state && $tanggalMulai) {
                                            $model = new CutiIzin([
                                                'tanggal_mulai' => $tanggalMulai,
                                                'tanggal_selesai' => $state
                                            ]);
                                            $set('jumlah_hari', $model->calculateJumlahHari());
                                        }
                                    }),
                            ]),

                        Forms\Components\TextInput::make('jumlah_hari')
                            ->label('Jumlah Hari Kerja')
                            ->numeric()
                            ->disabled()
                            ->helperText('Jumlah hari kerja akan dihitung otomatis (tidak termasuk weekend)')
                            ->suffix('hari'),

                        // Field untuk menampilkan informasi real-time penggunaan kuota
                        Forms\Components\Placeholder::make('quota_usage_info')
                            ->label('📊 Simulasi Penggunaan Kuota')
                            ->content(function (callable $get) {
                                $jenisPermohonan = $get('jenis_permohonan');
                                $jumlahHari = $get('jumlah_hari');

                                if ($jenisPermohonan !== 'cuti' || !$jumlahHari) {
                                    return new \Illuminate\Support\HtmlString('
                                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200">
                                            <p class="text-sm text-gray-600">Pilih "Cuti" dan tentukan tanggal untuk melihat simulasi penggunaan kuota</p>
                                        </div>
                                    ');
                                }

                                $user = auth()->user();
                                if (!$user || !$user->karyawan) {
                                    return new \Illuminate\Support\HtmlString('
                                        <div class="p-3 bg-gray-50 rounded-lg border border-gray-200">
                                            <p class="text-sm text-gray-600">Informasi kuota tidak tersedia</p>
                                        </div>
                                    ');
                                }

                                $karyawan = $user->karyawan;
                                $cutiModel = new \App\Models\CutiIzin();
                                $remainingQuota = $cutiModel->getRemainingLeaveQuota($karyawan->id);
                                $afterUsage = $remainingQuota - $jumlahHari;

                                $statusColor = $afterUsage >= 0 ? ($afterUsage > 3 ? "green" : "yellow") : "red";
                                $statusIcon = $afterUsage >= 0 ? "✅" : "❌";
                                $statusText = $afterUsage >= 0 ? "DAPAT DIAJUKAN" : "MELEBIHI KUOTA";

                                return new \Illuminate\Support\HtmlString("
                                    <div class=\"p-3 bg-{$statusColor}-50 rounded-lg border border-{$statusColor}-200\">
                                        <div class=\"space-y-2\">
                                            <div class=\"flex justify-between items-center\">
                                                <span class=\"text-sm font-medium text-{$statusColor}-800\">Hari yang akan digunakan:</span>
                                                <span class=\"text-sm font-bold text-{$statusColor}-900\">{$jumlahHari} hari</span>
                                            </div>
                                            <div class=\"flex justify-between items-center\">
                                                <span class=\"text-sm text-{$statusColor}-700\">Sisa kuota saat ini:</span>
                                                <span class=\"text-sm text-{$statusColor}-800\">{$remainingQuota} hari</span>
                                            </div>
                                            <div class=\"flex justify-between items-center border-t border-{$statusColor}-200 pt-2\">
                                                <span class=\"text-sm font-medium text-{$statusColor}-800\">Sisa kuota setelah cuti:</span>
                                                <span class=\"text-sm font-bold text-{$statusColor}-900\">" . max(0, $afterUsage) . " hari</span>
                                            </div>
                                            <div class=\"flex items-center justify-center mt-2 pt-2 border-t border-{$statusColor}-200\">
                                                <span class=\"text-sm font-bold text-{$statusColor}-800\">{$statusIcon} {$statusText}</span>
                                            </div>
                                        </div>
                                    </div>
                                ");
                            })
                            ->reactive(),

                        Forms\Components\Textarea::make('alasan')
                            ->label('Alasan')
                            ->required()
                            ->maxLength(1000)
                            ->placeholder('Jelaskan alasan permohonan cuti/izin Anda')
                            ->helperText('Jelaskan dengan jelas alasan permohonan cuti atau izin Anda')
                            ->rows(3),

                        Forms\Components\Textarea::make('keterangan_tambahan')
                            ->label('Keterangan Tambahan')
                            ->maxLength(1000)
                            ->placeholder('Informasi tambahan jika diperlukan')
                            ->helperText('Informasi tambahan yang mendukung permohonan (opsional)')
                            ->rows(2),

                        Forms\Components\FileUpload::make('dokumen_pendukung')
                            ->label('Dokumen Pendukung')
                            ->directory('cuti-izin/dokumen')
                            ->disk('public')
                            ->maxSize(5120)
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp', 'application/pdf'])
                            ->helperText('Upload dokumen pendukung jika diperlukan (maksimal 5MB)')
                            ->placeholder('Pilih file dokumen pendukung'),
                    ])
                    ->collapsible()
                    ->persistCollapsed(false),

                Forms\Components\Section::make('Informasi Status')
                    ->description('Status permohonan akan diperbarui setelah disetujui supervisor')
                    ->schema([
                        Forms\Components\Placeholder::make('status_info')
                            ->label('Status Permohonan')
                            ->content(function ($record) {
                                if (!$record) {
                                    return 'Permohonan baru akan berstatus "Menunggu Persetujuan"';
                                }

                                $statusText = $record->status_label;

                                if ($record->approved_by && $record->approved_at) {
                                    $approver = $record->approvedBy->name ?? 'Unknown';
                                    $approvedDate = $record->approved_at->format('d M Y H:i');
                                    $statusText .= " oleh {$approver} pada {$approvedDate}";
                                }

                                if ($record->status === 'rejected' && $record->rejection_reason) {
                                    $statusText .= "\nAlasan penolakan: {$record->rejection_reason}";
                                }

                                return $statusText;
                            }),
                    ])
                    ->visible(fn($record) => $record !== null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('jenis_permohonan')
                    ->label('Jenis')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'cuti' => 'Cuti',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                        default => ucfirst($state)
                    })
                    ->color(fn(string $state): string => match ($state) {
                        'cuti' => 'primary',
                        'izin' => 'info',
                        'sakit' => 'danger',
                        default => 'gray'
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('formatted_date_range')
                    ->label('Periode')
                    ->sortable(['tanggal_mulai']),

                Tables\Columns\TextColumn::make('jumlah_hari')
                    ->label('Jumlah Hari')
                    ->suffix(' hari')
                    ->alignCenter()
                    ->sortable(),

                Tables\Columns\TextColumn::make('alasan')
                    ->label('Alasan')
                    ->limit(50)
                    ->tooltip(function ($record) {
                        return $record->alasan;
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'pending' => 'Menunggu',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                        default => ucfirst($state)
                    })
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'gray'
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal Pengajuan')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_permohonan')
                    ->label('Jenis Permohonan')
                    ->options([
                        'cuti' => 'Cuti',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Menunggu Persetujuan',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                    ]),

                Tables\Filters\Filter::make('tanggal_mulai')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_mulai', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_selesai', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn($record) => $record->status === 'pending'),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn($record) => $record->status === 'pending'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn($records) => $records && $records->every(fn($record) => $record->status === 'pending')),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCutiIzins::route('/'),
            'create' => Pages\CreateCutiIzin::route('/create'),
            'view' => Pages\ViewCutiIzin::route('/{record}'),
            'edit' => Pages\EditCutiIzin::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $user = Auth::user();
        $karyawan = Karyawan::where('id_user', $user->id)->first();

        return parent::getEloquentQuery()
            ->with([
                'karyawan:id,nama_lengkap,nip',
                'approvedBy:id,name'
            ])
            ->where('karyawan_id', $karyawan?->id);
    }
}

<?php

namespace App\Filament\Karyawan\Widgets;

use App\Models\CutiIzin;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class CutiQuotaWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $user = auth()->user();
        if (!$user || !$user->karyawan) {
            return [];
        }

        $karyawan = $user->karyawan;

        // Get active contract
        $activeContract = $karyawan->riwayatKontrak()
            ->where('is_active', 1)
            ->orderBy('tgl_mulai', 'desc')
            ->first();

        if (!$activeContract) {
            return [
                Stat::make('Status Kontrak', 'Tidak Ada Kontrak Aktif')
                    ->description('Hubungi HRD untuk informasi lebih lanjut')
                    ->color('danger'),
            ];
        }

        $contractType = $activeContract->jenis_kontrak;

        // No quota for these contract types
        if (in_array($contractType, ['Freelance', 'Probation'])) {
            return [
                Stat::make('Jenis Ko<PERSON>rak', $contractType)
                    ->description('Tidak memiliki hak cuti tahunan')
                    ->color('gray'),

                Stat::make('Hak Cuti', 'Tidak Ada')
                    ->description('Hanya dapat mengajukan izin atau sakit')
                    ->color('warning'),
            ];
        }

        // Calculate quotas
        $cutiModel = new CutiIzin();
        $remainingQuota = $cutiModel->getRemainingLeaveQuota($karyawan->id);
        $yearlyQuota = 12;
        $usedQuota = $yearlyQuota - $remainingQuota;

        // Calculate monthly usage for PKWT
        $monthlyStats = [];
        if ($contractType === 'PKWT') {
            $currentMonth = Carbon::now()->month;
            $currentYear = Carbon::now()->year;

            $usedDaysThisMonth = CutiIzin::where('karyawan_id', $karyawan->id)
                ->where('jenis_permohonan', 'cuti')
                ->where('status', 'approved')
                ->whereYear('tanggal_mulai', $currentYear)
                ->whereMonth('tanggal_mulai', $currentMonth)
                ->sum('jumlah_hari');

            $monthlyQuota = 3;
            $remainingMonthlyQuota = max(0, $monthlyQuota - $usedDaysThisMonth);

            $monthlyStats[] = Stat::make('Kuota Bulan Ini', $remainingMonthlyQuota . ' hari')
                ->description("Dari {$monthlyQuota} hari per bulan (PKWT)")
                ->color($remainingMonthlyQuota > 0 ? 'success' : 'warning');
        }

        return array_merge([
            Stat::make('Jenis Kontrak', $contractType)
                ->description('Status kontrak saat ini')
                ->color(match ($contractType) {
                    'PKWTT' => 'success',
                    'PKWT' => 'warning',
                    default => 'gray',
                }),

            Stat::make('Sisa Kuota Tahun Ini', $remainingQuota . ' hari')
                ->description("Dari {$yearlyQuota} hari per tahun")
                ->color($remainingQuota > 3 ? 'success' : ($remainingQuota > 0 ? 'warning' : 'danger')),

            Stat::make('Cuti Terpakai', $usedQuota . ' hari')
                ->description('Total cuti yang sudah digunakan tahun ini')
                ->color($usedQuota < 9 ? 'success' : 'warning'),
        ], $monthlyStats);
    }
}

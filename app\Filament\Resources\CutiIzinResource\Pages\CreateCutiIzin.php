<?php

namespace App\Filament\Resources\CutiIzinResource\Pages;

use App\Filament\Resources\CutiIzinResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateCutiIzin extends CreateRecord
{
    protected static string $resource = CutiIzinResource::class;

    public function getTitle(): string
    {
        return 'Tambah Permohonan Cuti/Izin';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Validate dates and quota before creating
        $model = new \App\Models\CutiIzin($data);
        $errors = $model->validateDates();

        if (!empty($errors)) {
            foreach ($errors as $error) {
                // Check if it's a warning (monthly quota) or blocking error
                if (str_contains($error, '⚠️ Peringatan:')) {
                    \Filament\Notifications\Notification::make()
                        ->title('Peringatan Kuota Bulanan')
                        ->body($error)
                        ->warning()
                        ->send();
                } else {
                    \Filament\Notifications\Notification::make()
                        ->title('Validasi Gagal')
                        ->body($error)
                        ->danger()
                        ->send();

                    $this->halt();
                }
            }
        }

        // If the user is a supervisor or admin, they can auto-approve
        $user = Auth::user();
        if ($user->role === 'supervisor' || $user->role === 'admin') {
            $data['approved_by'] = $user->id;
            $data['approved_at'] = now();
            $data['status'] = 'approved';
        } else {
            $data['status'] = 'pending';
        }

        return $data;
    }
}

<?php

namespace App\Filament\Resources\KaryawanPermissionResource\Pages;

use App\Filament\Resources\KaryawanPermissionResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateKaryawanPermission extends CreateRecord
{
    protected static string $resource = KaryawanPermissionResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = Auth::id();

        // Convert scope_values to array if it's not already
        if (isset($data['scope_values']) && !is_array($data['scope_values'])) {
            $data['scope_values'] = [$data['scope_values']];
        }

        // Set scope_values to null for 'all' scope type
        if ($data['scope_type'] === 'all') {
            $data['scope_values'] = null;
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}

<?php

namespace App\Filament\Resources\KaryawanPermissionResource\Pages;

use App\Filament\Resources\KaryawanPermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditKaryawanPermission extends EditRecord
{
    protected static string $resource = KaryawanPermissionResource::class;

    protected static string $view = 'filament.resources.karyawan-permission-resource.pages.edit-karyawan-permission';

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Convert scope_values to array if it's not already
        if (isset($data['scope_values']) && !is_array($data['scope_values'])) {
            $data['scope_values'] = [$data['scope_values']];
        }

        // Set scope_values to null for 'all' scope type
        if ($data['scope_type'] === 'all') {
            $data['scope_values'] = null;
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}

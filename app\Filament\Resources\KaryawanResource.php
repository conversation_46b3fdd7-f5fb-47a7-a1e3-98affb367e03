<?php

namespace App\Filament\Resources;

use App\Models\Divisi;
use App\Models\Jabatan;
use App\Models\Karyawan;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ImageColumn;
use App\Filament\Resources\KaryawanResource\Pages;
use App\Filament\Resources\KaryawanResource\RelationManagers\RiwayatKontrakRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\PenggajianRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\PendidikanRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\KerabatRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\BpjsRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\ResignRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\KpiPenilaianRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\PelanggaranRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\DokumenRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\SchedulesRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\AbsensiRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\CutiIzinRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\MutasiPromosiDemosiRelationManager;
use App\Models\User;
use App\Models\MutasiPromosiDemosi;
use Illuminate\Support\Facades\Hash;
use Filament\Notifications\Notification;
use App\Exports\KaryawanExport;
use Illuminate\Support\Facades\Log;
use App\Traits\HasExportActions;


class KaryawanResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = Karyawan::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationGroup = 'Human Resource Management';
    protected static ?string $label = 'Data Karyawan';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Tabs::make('Form Karyawan')->tabs([
                Tab::make('Data Pribadi')->schema([
                    Grid::make(3)->schema([
                        TextInput::make('nama_lengkap')->label('Nama Lengkap')->required(),
                        TextInput::make('nip')->label('NIP'),
                        TextInput::make('nik')->label('NIK')
                            ->required()
                            ->numeric(),
                        TextInput::make('nomor_kk')->label('Nomor Kartu Keluarga')->nullable(),
                        Select::make('agama')
                            ->label('Agama')
                            ->options([
                                'Islam' => 'Islam',
                                'Kristen' => 'Kristen',
                                'Katolik' => 'Katolik',
                                'Hindu' => 'Hindu',
                                'Buddha' => 'Buddha',
                                'Konghucu' => 'Konghucu',
                            ])
                            ->nullable(),
                        Select::make('jenis_kelamin')
                            ->label('Jenis Kelamin')
                            ->options([
                                'Laki-laki' => 'Laki-laki',
                                'Perempuan' => 'Perempuan',
                            ])
                            ->required(),
                        Select::make('status_pernikahan')
                            ->label('Status Pernikahan')
                            ->options([
                                'Belum Menikah' => 'Belum Menikah',
                                'Menikah' => 'Menikah',
                                'Cerai' => 'Cerai',
                            ])
                            ->nullable(),
                        TextInput::make('jumlah_anak')->label('Jumlah Anak')
                            ->numeric()
                            ->nullable(),
                        TextInput::make('kota_lahir')
                            ->label('Kota Lahir')
                            ->required(),
                        DatePicker::make('tanggal_lahir')
                            ->label('Tanggal Lahir')
                            ->required()
                            ->displayFormat('d-m-Y')
                            ->firstDayOfWeek(1),
                        Select::make('status_aktif')
                            ->label('Status Aktif')
                            ->options([
                                1 => 'Aktif',
                                0 => 'Tidak Aktif',
                            ])
                            ->required()
                            ->native(false),
                    ]),
                ]),

                Tab::make('Kontak')->schema([
                    Grid::make(2)->schema([
                        TextInput::make('nomor_telepon')
                            ->label('Nomor Telepon')
                            ->required()
                            ->tel()
                            ->regex('/^[0-9+\-\s()]+$/')
                            ->minLength(config('app_constants.validation.phone_min_length', 10))
                            ->maxLength(config('app_constants.validation.phone_max_length', 15))
                            ->helperText('Hanya boleh berisi angka, +, -, spasi, dan tanda kurung'),

                        TextInput::make('alamat')
                            ->label('Alamat Domisili')
                            ->required(),

                        TextInput::make('alamat_ktp')
                            ->label('Alamat KTP')
                            ->nullable(),

                        TextInput::make('nama_ibu_kandung')
                            ->label('Nama Ibu Kandung')
                            ->nullable(),

                        Select::make('golongan_darah')
                            ->label('Golongan Darah')
                            ->options([
                                'A' => 'A',
                                'B' => 'B',
                                'AB' => 'AB',
                                'O' => 'O',
                            ])
                            ->nullable(),

                        TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->required()
                            ->rule(function ($record) {
                                return function ($attribute, $value, $fail) use ($record) {
                                    $karyawanExists = \App\Models\Karyawan::where('email', $value)
                                        ->where('id', '!=', $record?->id)
                                        ->exists();
                                    if ($karyawanExists) {
                                        $fail('Email sudah digunakan di tabel Karyawan.');
                                        return;
                                    }

                                    $userExists = \App\Models\User::where('email', $value)
                                        ->where('id', '!=', $record?->id_user)
                                        ->exists();
                                    if ($userExists) {
                                        $fail('Email sudah digunakan di tabel Users.');
                                    }
                                };
                            }),
                    ]),
                ]),

                Tab::make('Foto')->schema([
                    FileUpload::make('foto_profil')
                        ->label('Foto Profil')
                        ->image()
                        ->directory(config('app_constants.upload_directories.employee_photos'))
                        ->disk('public')
                        ->nullable(),
                ]),

                Tab::make('Posisi Awal')->schema([
                    Grid::make(2)->schema([
                        Select::make('id_entitas')
                            ->label('Entitas')
                            ->options(\App\Models\Entitas::pluck('nama', 'id'))
                            ->searchable()
                            ->preload()
                            ->required()

                            ->dehydrated() // <- wajib
                            ->createOptionForm([
                                TextInput::make('nama')->label('Nama Entitas')->required(),
                                Textarea::make('alamat')->label('Alamat')->nullable(),
                                Textarea::make('keterangan')->label('Keterangan')->nullable(),
                            ])
                            ->createOptionUsing(fn(array $data) => \App\Models\Entitas::create($data)->id),

                        Select::make('id_departemen')
                            ->label('Departemen')
                            ->options(\App\Models\Departemen::pluck('nama_departemen', 'id'))
                            ->searchable()
                            ->preload()
                            ->required()
                            ->reactive() // Make it reactive for dependent dropdown
                            ->afterStateUpdated(function (callable $set) {
                                // Reset divisi when departemen changes
                                $set('id_divisi', null);
                            })
                            ->dehydrated() // <- wajib
                            ->createOptionForm([
                                TextInput::make('nama_departemen')->label('Nama Departemen')->required(),
                            ])
                            ->createOptionUsing(fn(array $data) => \App\Models\Departemen::create($data)->id),

                        Select::make('id_divisi')
                            ->label('Divisi')
                            ->options(function (callable $get) {
                                $departemenId = $get('id_departemen');
                                if (!$departemenId) {
                                    return [];
                                }
                                return \App\Models\Divisi::where('departemen_id', $departemenId)
                                    ->pluck('nama_divisi', 'id');
                            })
                            ->searchable()
                            ->required()
                            ->disabled(fn(callable $get) => !$get('id_departemen')) // Disable until departemen is selected
                            ->placeholder(fn(callable $get) => $get('id_departemen') ? 'Pilih divisi...' : 'Pilih departemen terlebih dahulu')
                            ->helperText('Divisi akan muncul setelah memilih departemen')
                            ->dehydrated() // <- wajib
                            ->createOptionForm([
                                Select::make('departemen_id')
                                    ->label('Departemen')
                                    ->options(\App\Models\Departemen::pluck('nama_departemen', 'id'))
                                    ->searchable()
                                    ->required(),
                                TextInput::make('nama_divisi')->label('Nama Divisi')->required(),
                                Textarea::make('deskripsi')->label('Deskripsi')->nullable(),
                            ])
                            ->createOptionUsing(fn(array $data) => \App\Models\Divisi::create($data)->id),

                        Select::make('id_jabatan')
                            ->label('Jabatan')
                            ->options(\App\Models\Jabatan::pluck('nama_jabatan', 'id'))
                            ->searchable()
                            ->preload()
                            ->required()

                            ->dehydrated() // <- wajib
                            ->createOptionForm([
                                TextInput::make('nama_jabatan')->label('Nama Jabatan')->required(),
                                Textarea::make('deskripsi')->label('Deskripsi')->nullable(),
                            ])
                            ->createOptionUsing(fn(array $data) => \App\Models\Jabatan::create($data)->id),
                    ])
                ]),

            ])
                ->columnSpanFull(),
        ]);
    }

    public static function afterCreate(Form $form, $record): void
    {
        // Logic sudah dipindahkan ke CreateAction untuk menghindari duplikasi
        // dan untuk handling user creation yang lebih baik
    }

    public static function afterUpdate(Form $form, $record): void
    {

        // create user account with this email not exists
        // Cek apakah user dengan email ini sudah ada
        $existingUser = \App\Models\User::where('email', $record->email)->first();

        if (!$existingUser) {
            // Jika user belum ada, buat user baru
            $password = 'viera123';
            $user = \App\Models\User::create([
                'name'     => $record->nama_lengkap,
                'email'    => $record->email,
                'password' => Hash::make($password),
                'role'     => 'karyawan',
                'karyawan_id' => $record->id,
            ]);

            $record->update(['id_user' => $user->id]);

            // add permission shield ke karyawan setiap di create
            $user->assignRole('karyawan');

            \Filament\Notifications\Notification::make()
                ->title('User berhasil dibuat')
                ->body("Email: {$user->email}\nPassword: {$password}")
                ->success()
                ->send();
        }

        Log::info('AFTER UPDATE CALLED', [
            'id' => $record->id,
            'updated_at' => $record->updated_at,
            'data' => $record->toArray(),
        ]);

        $original = $record->getOriginal();

        $posisiSebelumnyaKosong = empty($original['id_entitas']) ||
            empty($original['id_departemen']) ||
            empty($original['id_divisi']) ||
            empty($original['id_jabatan']);

        $posisiSekarangLengkap = filled($record->id_entitas) &&
            filled($record->id_departemen) &&
            filled($record->id_divisi) &&
            filled($record->id_jabatan);

        $entitasChanged    = $original['id_entitas'] != $record->id_entitas;
        $departemenChanged = $original['id_departemen'] != $record->id_departemen;
        $divisiChanged     = $original['id_divisi'] != $record->id_divisi;
        $jabatanChanged    = $original['id_jabatan'] != $record->id_jabatan;

        Log::info('CHANGE CHECK', [
            'posisiSebelumnyaKosong' => $posisiSebelumnyaKosong,
            'posisiSekarangLengkap' => $posisiSekarangLengkap,
            'changed' => compact('entitasChanged', 'departemenChanged', 'divisiChanged', 'jabatanChanged'),
        ]);

        if ($posisiSebelumnyaKosong && $posisiSekarangLengkap) {
            Log::info('MENYIMPAN POSISI AWAL');

            MutasiPromosiDemosi::create([
                'karyawan_id'     => $record->id,
                'tipe'            => 'posisi_awal',
                'entitas_id'      => $record->id_entitas,
                'departemen_id'   => $record->id_departemen,
                'divisi_id'       => $record->id_divisi,
                'jabatan_id'      => $record->id_jabatan,
                'tanggal_efektif' => now(),
                'alasan'          => 'Input posisi awal setelah record dibuat.',
                'is_active'       => true,
            ]);
        } elseif (!$posisiSebelumnyaKosong && ($entitasChanged || $departemenChanged || $divisiChanged || $jabatanChanged)) {
            Log::info('MENYIMPAN MUTASI');

            $record->mutasiPromosiDemosi()->update(['is_active' => false]);

            MutasiPromosiDemosi::create([
                'karyawan_id'     => $record->id,
                'tipe'            => 'mutasi',
                'entitas_id'      => $record->id_entitas,
                'departemen_id'   => $record->id_departemen,
                'divisi_id'       => $record->id_divisi,
                'jabatan_id'      => $record->id_jabatan,
                'tanggal_efektif' => now(),
                'alasan'          => 'Mutasi dari edit form.',
                'is_active'       => true,
            ]);
        }
    }











    public static function table(Table $table): Table
    {
        return $table->columns([
            ImageColumn::make('foto_profil')
                ->label('Foto')
                ->url(fn($record) => asset('storage/' . $record->foto_profil))
                ->circular()
                ->width(50)
                ->height(50)
                ->alignCenter(),

            TextColumn::make('nama_lengkap')
                ->label('Nama')
                ->searchable()
                ->sortable()
                ->formatStateUsing(fn($state) => ucwords(strtolower($state)))
                ->alignCenter()
                ->weight('medium'),

            TextColumn::make('nip')
                ->label('NIP')
                ->searchable()
                ->copyable()
                ->alignCenter(),

            TextColumn::make('nik')
                ->label('NIK')
                ->copyable()
                ->alignCenter()
                ->toggleable(),

            TextColumn::make('tanggal_lahir')
                ->label('Tgl. Lahir')
                ->date('d M Y')
                ->sortable()
                ->alignCenter(),

            TextColumn::make('umur')

                ->label('Umur')
                ->getStateUsing(fn($record) => $record->umur)
                ->sortable()
                ->alignCenter(),

            TextColumn::make('entitas.nama')

                ->label('Entitas')
                ->sortable()
                ->formatStateUsing(fn($state) => ucwords(strtolower($state)))
                ->alignCenter()
                ->toggleable(),

            TextColumn::make('departemen.nama_departemen')

                ->label('Departemen')
                ->sortable()
                ->formatStateUsing(fn($state) => ucwords(strtolower($state)))
                ->alignCenter()
                ->toggleable(),


            TextColumn::make('jabatan.nama_jabatan')

                ->label('Jabatan')
                ->sortable()
                ->formatStateUsing(fn($state) => ucwords(strtolower($state)))
                ->alignCenter()
                ->toggleable(),

            TextColumn::make('divisi.nama_divisi')

                ->label('Divisi')
                ->sortable()
                ->formatStateUsing(fn($state) => ucwords(strtolower($state)))
                ->alignCenter()
                ->toggleable(),

            TextColumn::make('status_aktif')
                ->label('Status')
                ->formatStateUsing(fn($state) => $state ? '🟢 Aktif' : '🔴 Tidak Aktif')
                ->badge()
                ->color(fn($state) => $state ? 'success' : 'gray')
                ->alignCenter(),
            TextColumn::make('jenis_kontrak')
                ->label('Jenis Kontrak')
                ->getStateUsing(function ($record) {
                    $kontrak = $record->riwayatKontrak()
                        ->where('is_active', 1)
                        ->orderBy('tgl_mulai', 'desc')
                        ->first();

                    return $kontrak ? $kontrak->jenis_kontrak : '—';
                })
                ->badge()
                ->color(fn(string $state): string => match ($state) {
                    'PKWTT' => 'success',
                    'PKWT' => 'warning',
                    'Probation' => 'info',
                    'Freelance' => 'gray',
                    default => 'secondary',
                })
                ->alignCenter()
                ->toggleable(),

            TextColumn::make('sisa_kontrak')
                ->label('🕒 Sisa Kontrak')
                ->getStateUsing(function ($record) {
                    $kontrak = $record->riwayatKontrak()
                        ->where('is_active', 1)
                        ->latest('tgl_selesai')
                        ->first();

                    if (! $kontrak) {
                        return '—';
                    }

                    if (strtolower($kontrak->jenis_kontrak) === 'pkwtt') {
                        return 'Karyawan Tetap';
                    }

                    if (! $kontrak->tgl_selesai) {
                        return '—';
                    }

                    $tglSelesai = \Carbon\Carbon::parse($kontrak->tgl_selesai);
                    $selisih = now()->diffInDays($tglSelesai, false);

                    if ($selisih === 0) {
                        return 'Hari ini';
                    } elseif ($selisih < 0) {
                        return 'Lewat ' . abs($selisih) . ' hari';
                    }

                    return $selisih . ' hari lagi';
                })
                ->badge()
                ->color(function ($state) {
                    if ($state === 'Karyawan Tetap') {
                        return 'info'; // biru
                    }

                    if ($state === '—' || $state === 'Hari ini' || str_contains($state, 'Lewat')) {
                        return 'gray';
                    }

                    $days = (int) filter_var($state, FILTER_SANITIZE_NUMBER_INT);

                    return match (true) {
                        $days <= 30 => 'danger',
                        $days <= 45 => 'warning',
                        default => 'success',
                    };
                })
                ->alignCenter(),





        ])
            ->filters([

                Tables\Filters\SelectFilter::make('id_entitas')
                    ->label('Filter Entitas')
                    ->options(\App\Models\Entitas::pluck('nama', 'id'))
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('id_departemen')
                    ->label('Filter Departemen')
                    ->options(\App\Models\Departemen::pluck('nama_departemen', 'id'))
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('id_divisi')
                    ->label('Filter Divisi')
                    ->options(\App\Models\Divisi::pluck('nama_divisi', 'id'))
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('id_jabatan')
                    ->label('Filter Jabatan')
                    ->options(\App\Models\Jabatan::pluck('nama_jabatan', 'id'))
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('status_aktif')
                    ->label('Filter Status')
                    ->options([
                        1 => 'Aktif',
                        0 => 'Tidak Aktif',
                    ]),

                Tables\Filters\Filter::make('jenis_kontrak')
                    ->label('Filter Jenis Kontrak')
                    ->form([
                        \Filament\Forms\Components\Select::make('jenis_kontrak')
                            ->label('Jenis Kontrak')
                            ->options([
                                'PKWTT' => 'PKWTT (Tetap)',
                                'PKWT' => 'PKWT (Kontrak)',
                                'Probation' => 'Probation',
                                'Freelance' => 'Freelance',
                            ])
                            ->placeholder('Pilih Jenis Kontrak'),
                    ])
                    ->query(function ($query, array $data) {
                        if (filled($data['jenis_kontrak'])) {
                            return $query->whereHas('riwayatKontrak', function ($q) use ($data) {
                                $q->where('jenis_kontrak', $data['jenis_kontrak'])
                                    ->where('is_active', 1);
                            });
                        }
                        return $query;
                    }),

                Tables\Filters\Filter::make('kontrak_mau_habis')
                    ->label('Kontrak < 30 Hari')
                    ->query(function ($query) {
                        return $query->whereHas('riwayatKontrak', function ($q) {
                            $q->where('is_active', 1)
                                ->whereDate('tgl_selesai', '<=', now()->copy()->addDays(30));
                        });
                    }),

                Tables\Filters\Filter::make('ulang_tahun_bulan_ini')
                    ->label('Ulang Tahun Bulan Ini')
                    ->query(fn($query) => $query->whereMonth('tanggal_lahir', now()->month))
                    ->toggle(),

                Tables\Filters\Filter::make('belum_ada_gaji')
                    ->label('Belum Ada Data Gaji')
                    ->query(function ($query) {
                        return $query->whereDoesntHave('penggajian');
                    })
                    ->toggle(),

                Tables\Filters\Filter::make('belum_ada_pendidikan')
                    ->label('Belum Ada Data Pendidikan')
                    ->query(function ($query) {
                        return $query->whereDoesntHave('pendidikan');
                    })
                    ->toggle(),

                Tables\Filters\SelectFilter::make('user_status')
                    ->label('Status User')
                    ->options([
                        'linked' => 'Sudah Terkait',
                        'unlinked' => 'Belum Terkait',
                    ])
                    ->query(function ($query, array $data) {
                        return $query->when(
                            $data['value'] === 'linked',
                            fn($query) => $query->whereNotNull('id_user'),
                        )->when(
                            $data['value'] === 'unlinked',
                            fn($query) => $query->whereNull('id_user'),
                        );
                    }),

            ])

            ->headerActions([
                ...self::getExportActions(KaryawanExport::class, 'Data Karyawan'),

                Tables\Actions\CreateAction::make()
                    ->using(function (array $data) {
                        // Simpan karyawan lebih dulu
                        $karyawan = \App\Models\Karyawan::create($data);

                        // Simpan ke tabel mutasi_promosi_demosi sebagai posisi_awal
                        \App\Models\MutasiPromosiDemosi::create([
                            'karyawan_id'     => $karyawan->id,
                            'tipe'            => 'posisi_awal',
                            'entitas_id'      => $data['id_entitas'],
                            'departemen_id'   => $data['id_departemen'],
                            'divisi_id'       => $data['id_divisi'],
                            'jabatan_id'      => $data['id_jabatan'],
                            'tanggal_efektif' => now(),
                            'alasan'          => 'Penempatan awal saat pembuatan data karyawan.',
                            'is_active'       => true,
                        ]);

                        // Jika email diisi, cek atau buat akun user
                        if (!empty($data['email'])) {
                            // Cek apakah user dengan email ini sudah ada
                            $existingUser = \App\Models\User::with('karyawan')->where('email', $karyawan->email)->first();

                            if ($existingUser) {
                                // Jika user sudah ada, link ke karyawan ini
                                $karyawan->update(['id_user' => $existingUser->id]);

                                \Filament\Notifications\Notification::make()
                                    ->title('User sudah ada')
                                    ->body("Karyawan berhasil ditambahkan dan dikaitkan dengan user yang sudah ada: {$existingUser->email}")
                                    ->warning()
                                    ->send();
                            } else {
                                // Jika user belum ada, buat user baru
                                $password = 'viera123';
                                $user = \App\Models\User::create([
                                    'name'     => $karyawan->nama_lengkap,
                                    'email'    => $karyawan->email,
                                    'password' => Hash::make($password),
                                    'role'     => 'karyawan',
                                    'karyawan_id' => $karyawan->id,
                                ]);

                                $karyawan->update(['id_user' => $user->id]);

                                // add permission shield ke karyawan setiap di create
                                $user->assignRole('karyawan');

                                \Filament\Notifications\Notification::make()
                                    ->title('User berhasil dibuat')
                                    ->body("Email: {$user->email}\nPassword: {$password}")
                                    ->success()
                                    ->send();
                            }
                        }

                        return $karyawan;
                    }),




            ])

            ->actions([
                Tables\Actions\ViewAction::make()->tooltip('Lihat detail')->icon('heroicon-o-eye'),
                Tables\Actions\EditAction::make()->tooltip('Edit')->icon('heroicon-o-pencil'),
                Tables\Actions\DeleteAction::make()->tooltip('Hapus')->icon('heroicon-o-trash'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->searchable()
            ->striped()
            ->defaultSort('nama_lengkap');
    }

    public static function getRelations(): array
    {
        return [
            RiwayatKontrakRelationManager::class,
            PenggajianRelationManager::class,
            PendidikanRelationManager::class,
            KerabatRelationManager::class,
            BpjsRelationManager::class,
            ResignRelationManager::class,
            KpiPenilaianRelationManager::class,
            PelanggaranRelationManager::class,
            DokumenRelationManager::class,
            SchedulesRelationManager::class,
            AbsensiRelationManager::class,
            CutiIzinRelationManager::class,
            MutasiPromosiDemosiRelationManager::class,
        ];
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKaryawans::route('/'),
            'view' => Pages\ViewKaryawan::route('/{record}'),
            'edit' => Pages\EditKaryawan::route('/{record}/edit'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            // Widgets moved to dedicated HR Dashboard
            // Access via: HR Dashboard in navigation
        ];
    }

    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()->withBasicRelations();
    }
}

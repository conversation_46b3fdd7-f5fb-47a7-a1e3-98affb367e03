<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Resources\RelationManagers\RelationManager;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class CutiIzinRelationManager extends RelationManager
{
    protected static string $relationship = 'cutiIzin';
    protected static ?string $recordTitleAttribute = 'jenis_permohonan';
    protected static ?string $title = 'Cuti dan Izin';

    public function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            // Section untuk menampilkan informasi kuota cuti
            Forms\Components\Section::make('📊 Informasi Kuota Cuti Karyawan')
                ->description('Status kuota cuti karyawan berdasarkan jenis kontrak')
                ->schema([
                    Forms\Components\Placeholder::make('quota_info')
                        ->label('')
                        ->content(function ($livewire) {
                            $karyawan = $livewire->getOwnerRecord();
                            if (!$karyawan) {
                                return new \Illuminate\Support\HtmlString('
                                    <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
                                        <p class="text-sm text-gray-600">Informasi kuota tidak tersedia</p>
                                    </div>
                                ');
                            }

                            $activeContract = $karyawan->riwayatKontrak()
                                ->where('is_active', 1)
                                ->orderBy('tgl_mulai', 'desc')
                                ->first();

                            if (!$activeContract) {
                                return new \Illuminate\Support\HtmlString('
                                    <div class="p-4 bg-red-50 rounded-lg border border-red-200">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <h3 class="text-sm font-medium text-red-800">Karyawan Tidak Memiliki Kontrak Aktif</h3>
                                                <p class="text-sm text-red-700 mt-1">Silahkan tambahkan kontrak untuk karyawan ini</p>
                                            </div>
                                        </div>
                                    </div>
                                ');
                            }

                            $contractType = $activeContract->jenis_kontrak;

                            // Jika tidak memiliki hak cuti
                            if (in_array($contractType, ['Freelance', 'Probation'])) {
                                return new \Illuminate\Support\HtmlString('
                                    <div class="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <h3 class="text-sm font-medium text-yellow-800">Jenis Kontrak: ' . $contractType . '</h3>
                                                <p class="text-sm text-yellow-700 mt-1">❌ <strong>TIDAK MEMILIKI HAK CUTI</strong></p>
                                                <p class="text-sm text-yellow-600 mt-1">Karyawan hanya dapat mengajukan <strong>Izin</strong> atau <strong>Sakit</strong></p>
                                            </div>
                                        </div>
                                    </div>
                                ');
                            }

                            // Jika memiliki hak cuti (PKWT/PKWTT)
                            $cutiModel = new \App\Models\CutiIzin();
                            $remainingQuota = $cutiModel->getRemainingLeaveQuota($karyawan->id);
                            $usedQuota = 12 - $remainingQuota;

                            // Hitung kuota bulanan untuk PKWT
                            $monthlyInfo = "";
                            if ($contractType === "PKWT") {
                                $currentMonth = \Carbon\Carbon::now()->month;
                                $currentYear = \Carbon\Carbon::now()->year;

                                $usedDaysThisMonth = \App\Models\CutiIzin::where("karyawan_id", $karyawan->id)
                                    ->where("jenis_permohonan", "cuti")
                                    ->where("status", "approved")
                                    ->whereYear("tanggal_mulai", $currentYear)
                                    ->whereMonth("tanggal_mulai", $currentMonth)
                                    ->sum("jumlah_hari");

                                $remainingMonthly = max(0, 3 - $usedDaysThisMonth);
                                $monthlyInfo = "
                                    <div class=\"mt-2 pt-2 border-t border-blue-200\">
                                        <p class=\"text-sm text-blue-700\">📅 <strong>Kuota Bulanan:</strong> {$remainingMonthly} hari tersisa (dari 3 hari/bulan)</p>
                                        <p class=\"text-xs text-blue-600 mt-1\">* Batas bulanan hanya sebagai peringatan, tidak memblokir pengajuan</p>
                                    </div>
                                ";
                            }

                            $statusColor = $remainingQuota > 6 ? "green" : ($remainingQuota > 3 ? "yellow" : "red");
                            $statusIcon = $remainingQuota > 6 ? "✅" : ($remainingQuota > 3 ? "⚠️" : "🚨");

                            return new \Illuminate\Support\HtmlString("
                                <div class=\"p-4 bg-{$statusColor}-50 rounded-lg border border-{$statusColor}-200\">
                                    <div class=\"flex items-start\">
                                        <div class=\"flex-shrink-0\">
                                            <svg class=\"h-5 w-5 text-{$statusColor}-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">
                                                <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\" />
                                            </svg>
                                        </div>
                                        <div class=\"ml-3 flex-1\">
                                            <h3 class=\"text-sm font-medium text-{$statusColor}-800\">Karyawan: {$karyawan->nama_lengkap} - Kontrak: {$contractType}</h3>
                                            <div class=\"mt-2 space-y-1\">
                                                <p class=\"text-sm text-{$statusColor}-700\">{$statusIcon} <strong>Sisa Kuota Tahunan:</strong> {$remainingQuota} hari (dari 12 hari)</p>
                                                <p class=\"text-sm text-{$statusColor}-700\">📊 <strong>Cuti Terpakai:</strong> {$usedQuota} hari</p>
                                                <p class=\"text-sm text-{$statusColor}-700\">✅ <strong>Status:</strong> " . ($remainingQuota > 0 ? "BOLEH MENGAJUKAN CUTI" : "KUOTA HABIS") . "</p>
                                            </div>
                                            {$monthlyInfo}
                                        </div>
                                    </div>
                                </div>
                            ");
                        })
                ])
                ->collapsible()
                ->collapsed(false),

            Forms\Components\Select::make('jenis_permohonan')
                ->label('Jenis Permohonan')
                ->options([
                    'cuti' => 'Cuti',
                    'izin' => 'Izin',
                    'sakit' => 'Sakit',
                ])
                ->required()
                ->reactive()
                ->helperText(function ($livewire) {
                    $karyawan = $livewire->getOwnerRecord();
                    if (!$karyawan) {
                        return 'Pilih jenis permohonan yang sesuai';
                    }

                    $activeContract = $karyawan->riwayatKontrak()
                        ->where('is_active', 1)
                        ->orderBy('tgl_mulai', 'desc')
                        ->first();

                    if (!$activeContract) {
                        return 'Karyawan tidak memiliki kontrak aktif';
                    }

                    $contractType = $activeContract->jenis_kontrak;

                    if (in_array($contractType, ['Freelance', 'Probation'])) {
                        return "⚠️ Jenis kontrak {$contractType} tidak memiliki hak cuti. Hanya bisa mengajukan izin atau sakit.";
                    }

                    $cutiModel = new \App\Models\CutiIzin();
                    $remainingQuota = $cutiModel->getRemainingLeaveQuota($karyawan->id);

                    return "📋 Sisa kuota cuti tahun ini: {$remainingQuota} hari (dari 12 hari). Pilih jenis permohonan yang sesuai.";
                }),

            Forms\Components\Grid::make(2)
                ->schema([
                    Forms\Components\DatePicker::make('tanggal_mulai')
                        ->label('Tanggal Mulai')
                        ->required()
                        ->minDate(Carbon::today())
                        ->reactive()
                        ->afterStateUpdated(function ($state, callable $set, callable $get) {
                            $tanggalSelesai = $get('tanggal_selesai');
                            if ($state && $tanggalSelesai) {
                                $model = new \App\Models\CutiIzin([
                                    'tanggal_mulai' => $state,
                                    'tanggal_selesai' => $tanggalSelesai
                                ]);
                                $set('jumlah_hari', $model->calculateJumlahHari());
                            }
                        }),

                    Forms\Components\DatePicker::make('tanggal_selesai')
                        ->label('Tanggal Selesai')
                        ->required()
                        ->minDate(fn(callable $get) => $get('tanggal_mulai') ?: Carbon::today())
                        ->reactive()
                        ->afterStateUpdated(function ($state, callable $set, callable $get) {
                            $tanggalMulai = $get('tanggal_mulai');
                            if ($state && $tanggalMulai) {
                                $model = new \App\Models\CutiIzin([
                                    'tanggal_mulai' => $tanggalMulai,
                                    'tanggal_selesai' => $state
                                ]);
                                $set('jumlah_hari', $model->calculateJumlahHari());
                            }
                        }),
                ]),

            Forms\Components\TextInput::make('jumlah_hari')
                ->label('Jumlah Hari Kerja')
                ->numeric()
                ->disabled()
                ->helperText('Akan dihitung otomatis'),

            // Field untuk menampilkan informasi real-time penggunaan kuota
            Forms\Components\Placeholder::make('quota_usage_info')
                ->label('📊 Simulasi Penggunaan Kuota')
                ->content(function (callable $get, $livewire) {
                    $jenisPermohonan = $get('jenis_permohonan');
                    $jumlahHari = $get('jumlah_hari');

                    if ($jenisPermohonan !== 'cuti' || !$jumlahHari) {
                        return new \Illuminate\Support\HtmlString('
                            <div class="p-3 bg-gray-50 rounded-lg border border-gray-200">
                                <p class="text-sm text-gray-600">Pilih "Cuti" dan tentukan tanggal untuk melihat simulasi penggunaan kuota</p>
                            </div>
                        ');
                    }

                    $karyawan = $livewire->getOwnerRecord();
                    if (!$karyawan) {
                        return new \Illuminate\Support\HtmlString('
                            <div class="p-3 bg-gray-50 rounded-lg border border-gray-200">
                                <p class="text-sm text-gray-600">Informasi kuota tidak tersedia</p>
                            </div>
                        ');
                    }

                    $cutiModel = new \App\Models\CutiIzin();
                    $remainingQuota = $cutiModel->getRemainingLeaveQuota($karyawan->id);
                    $afterUsage = $remainingQuota - $jumlahHari;

                    $statusColor = $afterUsage >= 0 ? ($afterUsage > 3 ? "green" : "yellow") : "red";
                    $statusIcon = $afterUsage >= 0 ? "✅" : "❌";
                    $statusText = $afterUsage >= 0 ? "DAPAT DIAJUKAN" : "MELEBIHI KUOTA";

                    return new \Illuminate\Support\HtmlString("
                        <div class=\"p-3 bg-{$statusColor}-50 rounded-lg border border-{$statusColor}-200\">
                            <div class=\"space-y-2\">
                                <div class=\"flex justify-between items-center\">
                                    <span class=\"text-sm font-medium text-{$statusColor}-800\">Hari yang akan digunakan:</span>
                                    <span class=\"text-sm font-bold text-{$statusColor}-900\">{$jumlahHari} hari</span>
                                </div>
                                <div class=\"flex justify-between items-center\">
                                    <span class=\"text-sm text-{$statusColor}-700\">Sisa kuota saat ini:</span>
                                    <span class=\"text-sm text-{$statusColor}-800\">{$remainingQuota} hari</span>
                                </div>
                                <div class=\"flex justify-between items-center border-t border-{$statusColor}-200 pt-2\">
                                    <span class=\"text-sm font-medium text-{$statusColor}-800\">Sisa kuota setelah cuti:</span>
                                    <span class=\"text-sm font-bold text-{$statusColor}-900\">" . max(0, $afterUsage) . " hari</span>
                                </div>
                                <div class=\"flex items-center justify-center mt-2 pt-2 border-t border-{$statusColor}-200\">
                                    <span class=\"text-sm font-bold text-{$statusColor}-800\">{$statusIcon} {$statusText}</span>
                                </div>
                            </div>
                        </div>
                    ");
                })
                ->reactive(),

            Forms\Components\Textarea::make('alasan')
                ->label('Alasan')
                ->required()
                ->maxLength(1000)
                ->rows(3),

            Forms\Components\Textarea::make('keterangan_tambahan')
                ->label('Keterangan Tambahan')
                ->maxLength(1000)
                ->rows(2),

            Forms\Components\FileUpload::make('dokumen_pendukung')
                ->label('Dokumen Pendukung')
                ->directory('cuti-izin/dokumen')
                ->disk('public')
                ->maxSize(5120)
                ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp', 'application/pdf']),

            Forms\Components\Select::make('status')
                ->label('Status')
                ->options([
                    'pending' => 'Menunggu Persetujuan',
                    'approved' => 'Disetujui',
                    'rejected' => 'Ditolak',
                ])
                ->required()
                ->default('pending'),

            Forms\Components\Textarea::make('rejection_reason')
                ->label('Alasan Penolakan')
                ->maxLength(1000)
                ->visible(fn(callable $get) => $get('status') === 'rejected')
                ->required(fn(callable $get) => $get('status') === 'rejected'),
        ]);
    }

    public function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->recordTitleAttribute('jenis_permohonan')
            ->columns([
                Tables\Columns\TextColumn::make('jenis_permohonan')
                    ->label('Jenis')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'cuti' => 'Cuti',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                        default => ucfirst($state)
                    })
                    ->color(fn(string $state): string => match ($state) {
                        'cuti' => 'primary',
                        'izin' => 'info',
                        'sakit' => 'danger',
                        default => 'gray'
                    }),

                Tables\Columns\TextColumn::make('formatted_date_range')
                    ->label('Periode'),

                Tables\Columns\TextColumn::make('jumlah_hari')
                    ->label('Jumlah Hari')
                    ->suffix(' hari')
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('alasan')
                    ->label('Alasan')
                    ->limit(30)
                    ->tooltip(function ($record) {
                        return $record->alasan;
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'pending' => 'Menunggu',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                        default => ucfirst($state)
                    })
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'gray'
                    }),

                Tables\Columns\TextColumn::make('approvedBy.name')
                    ->label('Disetujui Oleh')
                    ->placeholder('-'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal Pengajuan')
                    ->dateTime('d M Y')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_permohonan')
                    ->label('Jenis')
                    ->options([
                        'cuti' => 'Cuti',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Menunggu Persetujuan',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                    ]),

                Tables\Filters\Filter::make('tanggal')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_mulai', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_selesai', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('approve')
                    ->label('Setujui')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn($record) => (Auth::user()->role === 'supervisor' || Auth::user()->role === 'admin') && $record->status === 'pending')
                    ->action(function ($record) {
                        $record->update([
                            'status' => 'approved',
                            'approved_by' => Auth::id(),
                            'approved_at' => now(),
                            'rejection_reason' => null,
                        ]);

                        Notification::make()
                            ->title('Permohonan berhasil disetujui')
                            ->body('Permohonan ' . $record->jenis_permohonan_label . ' telah disetujui.')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('reject')
                    ->label('Tolak')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn($record) => (Auth::user()->role === 'supervisor' || Auth::user()->role === 'admin') && $record->status === 'pending')
                    ->form([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Alasan Penolakan')
                            ->required()
                            ->maxLength(1000)
                            ->placeholder('Jelaskan alasan penolakan permohonan ini'),
                    ])
                    ->action(function ($record, array $data) {
                        $record->update([
                            'status' => 'rejected',
                            'approved_by' => Auth::id(),
                            'approved_at' => now(),
                            'rejection_reason' => $data['rejection_reason'],
                        ]);

                        Notification::make()
                            ->title('Permohonan berhasil ditolak')
                            ->body('Permohonan ' . $record->jenis_permohonan_label . ' telah ditolak.')
                            ->success()
                            ->send();
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data) {
                        // If the user is a supervisor, auto-approve the request
                        $user = Auth::user();
                        if ($user->role === 'supervisor' || $user->role === 'admin') {
                            $data['approved_by'] = $user->id;
                            $data['approved_at'] = now();
                            $data['status'] = 'approved';
                        } else {
                            $data['status'] = 'pending';
                        }

                        return $data;
                    }),
            ])
            ->defaultSort('created_at', 'desc');
    }
}

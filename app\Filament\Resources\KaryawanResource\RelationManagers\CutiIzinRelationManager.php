<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Resources\RelationManagers\RelationManager;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class CutiIzinRelationManager extends RelationManager
{
    protected static string $relationship = 'cutiIzin';
    protected static ?string $recordTitleAttribute = 'jenis_permohonan';
    protected static ?string $title = 'Cuti dan Izin';

    public function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('jenis_permohonan')
                ->label('Jenis Permohonan')
                ->options([
                    'cuti' => 'Cuti',
                    'izin' => 'Izin',
                    'sakit' => 'Sakit',
                ])
                ->required()
                ->reactive()
                ->helperText(function ($livewire) {
                    $karyawan = $livewire->getOwnerRecord();
                    if (!$karyawan) {
                        return 'Pilih jenis permohonan yang sesuai';
                    }

                    $activeContract = $karyawan->riwayatKontrak()
                        ->where('is_active', 1)
                        ->orderBy('tgl_mulai', 'desc')
                        ->first();

                    if (!$activeContract) {
                        return 'Karyawan tidak memiliki kontrak aktif';
                    }

                    $contractType = $activeContract->jenis_kontrak;

                    if (in_array($contractType, ['Freelance', 'Probation'])) {
                        return "⚠️ Jenis kontrak {$contractType} tidak memiliki hak cuti. Hanya bisa mengajukan izin atau sakit.";
                    }

                    $cutiModel = new \App\Models\CutiIzin();
                    $remainingQuota = $cutiModel->getRemainingLeaveQuota($karyawan->id);

                    return "📋 Sisa kuota cuti tahun ini: {$remainingQuota} hari (dari 12 hari). Pilih jenis permohonan yang sesuai.";
                }),

            Forms\Components\Grid::make(2)
                ->schema([
                    Forms\Components\DatePicker::make('tanggal_mulai')
                        ->label('Tanggal Mulai')
                        ->required()
                        ->minDate(Carbon::today())
                        ->reactive()
                        ->afterStateUpdated(function ($state, callable $set, callable $get) {
                            $tanggalSelesai = $get('tanggal_selesai');
                            if ($state && $tanggalSelesai) {
                                $model = new \App\Models\CutiIzin([
                                    'tanggal_mulai' => $state,
                                    'tanggal_selesai' => $tanggalSelesai
                                ]);
                                $set('jumlah_hari', $model->calculateJumlahHari());
                            }
                        }),

                    Forms\Components\DatePicker::make('tanggal_selesai')
                        ->label('Tanggal Selesai')
                        ->required()
                        ->minDate(fn(callable $get) => $get('tanggal_mulai') ?: Carbon::today())
                        ->reactive()
                        ->afterStateUpdated(function ($state, callable $set, callable $get) {
                            $tanggalMulai = $get('tanggal_mulai');
                            if ($state && $tanggalMulai) {
                                $model = new \App\Models\CutiIzin([
                                    'tanggal_mulai' => $tanggalMulai,
                                    'tanggal_selesai' => $state
                                ]);
                                $set('jumlah_hari', $model->calculateJumlahHari());
                            }
                        }),
                ]),

            Forms\Components\TextInput::make('jumlah_hari')
                ->label('Jumlah Hari Kerja')
                ->numeric()
                ->disabled()
                ->helperText('Akan dihitung otomatis'),

            Forms\Components\Textarea::make('alasan')
                ->label('Alasan')
                ->required()
                ->maxLength(1000)
                ->rows(3),

            Forms\Components\Textarea::make('keterangan_tambahan')
                ->label('Keterangan Tambahan')
                ->maxLength(1000)
                ->rows(2),

            Forms\Components\FileUpload::make('dokumen_pendukung')
                ->label('Dokumen Pendukung')
                ->directory('cuti-izin/dokumen')
                ->disk('public')
                ->maxSize(5120)
                ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp', 'application/pdf']),

            Forms\Components\Select::make('status')
                ->label('Status')
                ->options([
                    'pending' => 'Menunggu Persetujuan',
                    'approved' => 'Disetujui',
                    'rejected' => 'Ditolak',
                ])
                ->required()
                ->default('pending'),

            Forms\Components\Textarea::make('rejection_reason')
                ->label('Alasan Penolakan')
                ->maxLength(1000)
                ->visible(fn(callable $get) => $get('status') === 'rejected')
                ->required(fn(callable $get) => $get('status') === 'rejected'),
        ]);
    }

    public function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->recordTitleAttribute('jenis_permohonan')
            ->columns([
                Tables\Columns\TextColumn::make('jenis_permohonan')
                    ->label('Jenis')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'cuti' => 'Cuti',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                        default => ucfirst($state)
                    })
                    ->color(fn(string $state): string => match ($state) {
                        'cuti' => 'primary',
                        'izin' => 'info',
                        'sakit' => 'danger',
                        default => 'gray'
                    }),

                Tables\Columns\TextColumn::make('formatted_date_range')
                    ->label('Periode'),

                Tables\Columns\TextColumn::make('jumlah_hari')
                    ->label('Jumlah Hari')
                    ->suffix(' hari')
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('alasan')
                    ->label('Alasan')
                    ->limit(30)
                    ->tooltip(function ($record) {
                        return $record->alasan;
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'pending' => 'Menunggu',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                        default => ucfirst($state)
                    })
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'gray'
                    }),

                Tables\Columns\TextColumn::make('approvedBy.name')
                    ->label('Disetujui Oleh')
                    ->placeholder('-'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal Pengajuan')
                    ->dateTime('d M Y')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_permohonan')
                    ->label('Jenis')
                    ->options([
                        'cuti' => 'Cuti',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Menunggu Persetujuan',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                    ]),

                Tables\Filters\Filter::make('tanggal')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_mulai', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_selesai', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('approve')
                    ->label('Setujui')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn($record) => (Auth::user()->role === 'supervisor' || Auth::user()->role === 'admin') && $record->status === 'pending')
                    ->action(function ($record) {
                        $record->update([
                            'status' => 'approved',
                            'approved_by' => Auth::id(),
                            'approved_at' => now(),
                            'rejection_reason' => null,
                        ]);

                        Notification::make()
                            ->title('Permohonan berhasil disetujui')
                            ->body('Permohonan ' . $record->jenis_permohonan_label . ' telah disetujui.')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('reject')
                    ->label('Tolak')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn($record) => (Auth::user()->role === 'supervisor' || Auth::user()->role === 'admin') && $record->status === 'pending')
                    ->form([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Alasan Penolakan')
                            ->required()
                            ->maxLength(1000)
                            ->placeholder('Jelaskan alasan penolakan permohonan ini'),
                    ])
                    ->action(function ($record, array $data) {
                        $record->update([
                            'status' => 'rejected',
                            'approved_by' => Auth::id(),
                            'approved_at' => now(),
                            'rejection_reason' => $data['rejection_reason'],
                        ]);

                        Notification::make()
                            ->title('Permohonan berhasil ditolak')
                            ->body('Permohonan ' . $record->jenis_permohonan_label . ' telah ditolak.')
                            ->success()
                            ->send();
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data) {
                        // If the user is a supervisor, auto-approve the request
                        $user = Auth::user();
                        if ($user->role === 'supervisor' || $user->role === 'admin') {
                            $data['approved_by'] = $user->id;
                            $data['approved_at'] = now();
                            $data['status'] = 'approved';
                        } else {
                            $data['status'] = 'pending';
                        }

                        return $data;
                    }),
            ])
            ->defaultSort('created_at', 'desc');
    }
}

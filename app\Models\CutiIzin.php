<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class CutiIzin extends Model
{
    use HasFactory;

    protected $table = 'cuti_izin';

    protected $fillable = [
        'karyawan_id',
        'jenis_permohonan',
        'tanggal_mulai',
        'tanggal_selesai',
        'jumlah_hari',
        'alasan',
        'keterangan_tambahan',
        'dokumen_pendukung',
        'status',
        'approved_by',
        'approved_at',
        'rejection_reason',
    ];

    protected $casts = [
        'tanggal_mulai' => 'date',
        'tanggal_selesai' => 'date',
        'approved_at' => 'datetime',
        'jumlah_hari' => 'integer',
    ];

    /**
     * Get the employee associated with this leave request
     */
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    /**
     * Get the supervisor who approved/rejected this request
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }



    /**
     * Check if the request is approved
     */
    public function getIsApprovedAttribute()
    {
        return $this->status === 'approved';
    }

    /**
     * Check if the request is pending
     */
    public function getIsPendingAttribute()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the request is rejected
     */
    public function getIsRejectedAttribute()
    {
        return $this->status === 'rejected';
    }

    /**
     * Get the status label
     */
    public function getStatusLabelAttribute()
    {
        return match ($this->status) {
            'pending' => 'Menunggu Persetujuan',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak',
            default => 'Tidak Diketahui'
        };
    }

    /**
     * Get the request type label
     */
    public function getJenisPermohonanLabelAttribute()
    {
        return match ($this->jenis_permohonan) {
            'cuti' => 'Cuti',
            'izin' => 'Izin',
            'sakit' => 'Sakit',
            default => 'Tidak Diketahui'
        };
    }

    /**
     * Get the status color for UI
     */
    public function getStatusColorAttribute()
    {
        return match ($this->status) {
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            default => 'gray'
        };
    }

    /**
     * Calculate the number of days automatically
     */
    public function calculateJumlahHari()
    {
        if (!$this->tanggal_mulai || !$this->tanggal_selesai) {
            return 0;
        }

        $startDate = Carbon::parse($this->tanggal_mulai);
        $endDate = Carbon::parse($this->tanggal_selesai);

        // Calculate working days (excluding weekends)
        $days = 0;
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            // Only count weekdays (Monday to Friday)
            if ($currentDate->isWeekday()) {
                $days++;
            }
            $currentDate->addDay();
        }

        return $days;
    }

    /**
     * Boot method to automatically calculate days
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            // Automatically calculate jumlah_hari when saving
            $model->jumlah_hari = $model->calculateJumlahHari();
        });
    }

    /**
     * Scope for filtering by employee
     */
    public function scopeForKaryawan($query, $karyawanId)
    {
        return $query->where('karyawan_id', $karyawanId);
    }

    /**
     * Scope for filtering by status
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for filtering by request type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('jenis_permohonan', $type);
    }

    /**
     * Scope for filtering by date range
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('tanggal_mulai', [$startDate, $endDate])
            ->orWhereBetween('tanggal_selesai', [$startDate, $endDate]);
    }

    /**
     * Check if dates overlap with existing approved requests
     */
    public function hasOverlapWithApprovedRequests()
    {
        return static::where('karyawan_id', $this->karyawan_id)
            ->where('status', 'approved')
            ->where('id', '!=', $this->id ?? 0)
            ->where(function ($query) {
                $query->whereBetween('tanggal_mulai', [$this->tanggal_mulai, $this->tanggal_selesai])
                    ->orWhereBetween('tanggal_selesai', [$this->tanggal_mulai, $this->tanggal_selesai])
                    ->orWhere(function ($subQuery) {
                        $subQuery->where('tanggal_mulai', '<=', $this->tanggal_mulai)
                            ->where('tanggal_selesai', '>=', $this->tanggal_selesai);
                    });
            })
            ->exists();
    }

    /**
     * Validate if the request dates are valid
     */
    public function validateDates()
    {
        $errors = [];

        // Check if start date is not in the past
        if ($this->tanggal_mulai && Carbon::parse($this->tanggal_mulai)->lt(Carbon::today())) {
            $errors[] = 'Tanggal mulai tidak boleh di masa lalu.';
        }

        // Validate leave quota based on contract type
        if ($this->jenis_permohonan === 'cuti' && $this->karyawan_id) {
            $quotaValidation = $this->validateLeaveQuota();
            if (!empty($quotaValidation)) {
                $errors = array_merge($errors, $quotaValidation);
            }
        }

        // Check if trying to apply for leave on today and already attended today
        if (
            $this->karyawan_id && $this->tanggal_mulai &&
            Carbon::parse($this->tanggal_mulai)->isSameDay(Carbon::today())
        ) {
            $hasAttendedToday = \App\Models\Absensi::where('karyawan_id', $this->karyawan_id)
                ->whereDate('tanggal_absensi', Carbon::today())
                ->whereNotNull('waktu_masuk')
                ->exists();

            if ($hasAttendedToday) {
                $errors[] = 'Tidak dapat mengajukan cuti/izin untuk hari ini karena sudah melakukan absensi.';
            }
        }

        // Check if end date is not before start date
        if (
            $this->tanggal_mulai && $this->tanggal_selesai &&
            Carbon::parse($this->tanggal_selesai)->lt(Carbon::parse($this->tanggal_mulai))
        ) {
            $errors[] = 'Tanggal selesai tidak boleh sebelum tanggal mulai.';
        }

        // Check if employee has already attended on the requested dates
        if ($this->karyawan_id && $this->tanggal_mulai && $this->tanggal_selesai) {
            $attendedDates = \App\Models\Absensi::where('karyawan_id', $this->karyawan_id)
                ->whereBetween('tanggal_absensi', [
                    Carbon::parse($this->tanggal_mulai)->format('Y-m-d'),
                    Carbon::parse($this->tanggal_selesai)->format('Y-m-d')
                ])
                ->whereNotNull('waktu_masuk')
                ->pluck('tanggal_absensi')
                ->map(function ($date) {
                    return Carbon::parse($date)->format('d-m-Y');
                })
                ->toArray();

            if (!empty($attendedDates)) {
                $errors[] = 'Tidak dapat mengajukan cuti/izin karena sudah melakukan absensi pada tanggal: ' . implode(', ', $attendedDates);
            }
        }

        // Check for overlapping approved requests
        if ($this->hasOverlapWithApprovedRequests()) {
            $errors[] = 'Tanggal yang dipilih bertabrakan dengan permohonan yang sudah disetujui.';
        }

        return $errors;
    }

    /**
     * Get formatted date range
     */
    public function getFormattedDateRangeAttribute()
    {
        if (!$this->tanggal_mulai || !$this->tanggal_selesai) {
            return '-';
        }

        $start = Carbon::parse($this->tanggal_mulai)->format('d M Y');
        $end = Carbon::parse($this->tanggal_selesai)->format('d M Y');

        if ($start === $end) {
            return $start;
        }

        return "{$start} - {$end}";
    }

    /**
     * Validate leave quota based on contract type
     */
    public function validateLeaveQuota()
    {
        $errors = [];

        if (!$this->karyawan_id) {
            return $errors;
        }

        $karyawan = $this->karyawan;
        if (!$karyawan) {
            return $errors;
        }

        // Get active contract
        $activeContract = $karyawan->riwayatKontrak()
            ->where('is_active', 1)
            ->orderBy('tgl_mulai', 'desc')
            ->first();

        if (!$activeContract) {
            $errors[] = 'Karyawan tidak memiliki kontrak aktif.';
            return $errors;
        }

        $contractType = $activeContract->jenis_kontrak;

        // Check if contract type allows leave
        if (in_array($contractType, ['Freelance', 'Probation'])) {
            $errors[] = "Karyawan dengan jenis kontrak {$contractType} tidak memiliki hak cuti.";
            return $errors;
        }

        // Calculate requested days
        $requestedDays = $this->calculateJumlahHari();

        // Get current year
        $currentYear = Carbon::now()->year;

        // Calculate used leave days this year (approved only)
        $usedDaysThisYear = static::where('karyawan_id', $this->karyawan_id)
            ->where('jenis_permohonan', 'cuti')
            ->where('status', 'approved')
            ->whereYear('tanggal_mulai', $currentYear)
            ->when($this->exists, function ($query) {
                // Exclude current record if updating
                return $query->where('id', '!=', $this->id);
            })
            ->sum('jumlah_hari');

        // Set quota based on contract type
        $yearlyQuota = match ($contractType) {
            'PKWT', 'PKWTT' => 12,
            default => 0
        };

        // Check yearly quota
        if (($usedDaysThisYear + $requestedDays) > $yearlyQuota) {
            $remainingQuota = max(0, $yearlyQuota - $usedDaysThisYear);
            $errors[] = "Kuota cuti tahunan tidak mencukupi. Sisa kuota: {$remainingQuota} hari, diminta: {$requestedDays} hari.";
        }

        // Check monthly quota for PKWT (notification only, not blocking)
        if ($contractType === 'PKWT') {
            $currentMonth = Carbon::now()->month;
            $currentYear = Carbon::now()->year;

            $usedDaysThisMonth = static::where('karyawan_id', $this->karyawan_id)
                ->where('jenis_permohonan', 'cuti')
                ->where('status', 'approved')
                ->whereYear('tanggal_mulai', $currentYear)
                ->whereMonth('tanggal_mulai', $currentMonth)
                ->when($this->exists, function ($query) {
                    return $query->where('id', '!=', $this->id);
                })
                ->sum('jumlah_hari');

            $monthlyQuota = 3;
            if (($usedDaysThisMonth + $requestedDays) > $monthlyQuota) {
                $remainingMonthlyQuota = max(0, $monthlyQuota - $usedDaysThisMonth);
                // This is just a warning, not blocking
                $errors[] = "⚠️ Peringatan: Batas cuti bulanan (3 hari) akan terlampaui. Sisa kuota bulan ini: {$remainingMonthlyQuota} hari, diminta: {$requestedDays} hari.";
            }
        }

        return $errors;
    }

    /**
     * Get remaining leave quota for the year
     */
    public function getRemainingLeaveQuota($karyawanId = null)
    {
        $karyawanId = $karyawanId ?: $this->karyawan_id;

        if (!$karyawanId) {
            return 0;
        }

        $karyawan = Karyawan::find($karyawanId);
        if (!$karyawan) {
            return 0;
        }

        // Get active contract
        $activeContract = $karyawan->riwayatKontrak()
            ->where('is_active', 1)
            ->orderBy('tgl_mulai', 'desc')
            ->first();

        if (!$activeContract) {
            return 0;
        }

        $contractType = $activeContract->jenis_kontrak;

        // No quota for these contract types
        if (in_array($contractType, ['Freelance', 'Probation'])) {
            return 0;
        }

        // Set quota based on contract type
        $yearlyQuota = match ($contractType) {
            'PKWT', 'PKWTT' => 12,
            default => 0
        };

        // Calculate used leave days this year
        $currentYear = Carbon::now()->year;
        $usedDaysThisYear = static::where('karyawan_id', $karyawanId)
            ->where('jenis_permohonan', 'cuti')
            ->where('status', 'approved')
            ->whereYear('tanggal_mulai', $currentYear)
            ->sum('jumlah_hari');

        return max(0, $yearlyQuota - $usedDaysThisYear);
    }
}

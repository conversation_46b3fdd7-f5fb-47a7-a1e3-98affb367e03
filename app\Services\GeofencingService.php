<?php

namespace App\Services;

use App\Models\Entitas;
use App\Models\Karyawan;

class GeofencingService
{
    /**
     * Validate if user can perform attendance based on location
     *
     * @param Karyawan $karyawan
     * @param float $userLat
     * @param float $userLon
     * @return array
     */
    public static function validateAttendanceLocation(Karyawan $karyawan, float $userLat, float $userLon): array
    {
        // Get karyawan's entitas
        $entitas = $karyawan->entitas;

        if (!$entitas) {
            return [
                'allowed' => false,
                'message' => 'Entitas karyawan tidak ditemukan',
                'distance' => 0,
                'radius' => 0,
                'error' => true
            ];
        }

        // Check geofencing validation
        $validation = $entitas->isWithinRadius($userLat, $userLon);

        return [
            'allowed' => $validation['allowed'],
            'message' => $validation['message'],
            'distance' => $validation['distance'],
            'radius' => $validation['radius'],
            'entitas_name' => $entitas->nama,
            'entitas_coordinates' => $entitas->coordinates,
            'user_coordinates' => "{$userLat}, {$userLon}",
            'geofencing_enabled' => $entitas->enable_geofencing,
            'error' => false
        ];
    }

    /**
     * Get attendance location info for display
     *
     * @param Karyawan $karyawan
     * @return array
     */
    public static function getAttendanceLocationInfo(Karyawan $karyawan): array
    {
        // Prioritas 1: Cek jadwal kerja hari ini untuk mendapatkan entitas
        $today = \Carbon\Carbon::today()->format('Y-m-d');
        $jadwal = \App\Models\Schedule::where('karyawan_id', $karyawan->id)
            ->whereDate('tanggal_jadwal', $today)
            ->with('entitas')
            ->first();

        $entitas = null;
        $source = '';

        // Prioritas 1: Gunakan entitas dari jadwal kerja jika ada
        if ($jadwal && $jadwal->entitas_id) {
            $entitas = $jadwal->entitas ?? \App\Models\Entitas::find($jadwal->entitas_id);
            $source = 'jadwal kerja hari ini';
        }

        // Prioritas 2: Jika tidak ada entitas di jadwal, gunakan entitas karyawan
        if (!$entitas && $karyawan->entitas) {
            $entitas = $karyawan->entitas;
            $source = 'entitas default karyawan';
        }

        if (!$entitas) {
            return [
                'has_location' => false,
                'message' => 'Entitas tidak ditemukan. Pastikan Anda memiliki jadwal kerja atau entitas default.'
            ];
        }

        if (!$entitas->enable_geofencing) {
            return [
                'has_location' => false,
                'message' => "Geofencing tidak diaktifkan untuk entitas {$entitas->nama}"
            ];
        }

        if (!$entitas->latitude || !$entitas->longitude) {
            return [
                'has_location' => false,
                'message' => "Koordinat entitas {$entitas->nama} belum diatur"
            ];
        }

        return [
            'has_location' => true,
            'entitas_name' => $entitas->nama,
            'coordinates' => $entitas->coordinates,
            'radius' => $entitas->radius,
            'address' => $entitas->alamat,
            'source' => $source,
            'message' => "Anda harus berada dalam radius {$entitas->radius}m dari {$entitas->nama} (berdasarkan {$source})"
        ];
    }

    /**
     * Calculate distance between two points
     *
     * @param float $lat1
     * @param float $lon1
     * @param float $lat2
     * @param float $lon2
     * @return float Distance in meters
     */
    public static function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        return Entitas::calculateDistance($lat1, $lon1, $lat2, $lon2);
    }

    /**
     * Format distance for display
     *
     * @param float $distance Distance in meters
     * @return string
     */
    public static function formatDistance(float $distance): string
    {
        if ($distance < 1000) {
            return round($distance, 1) . 'm';
        } else {
            return round($distance / 1000, 2) . 'km';
        }
    }

    /**
     * Get location status badge info
     *
     * @param array $validation
     * @return array
     */
    public static function getLocationStatusBadge(array $validation): array
    {
        if ($validation['error']) {
            return [
                'color' => 'danger',
                'icon' => 'heroicon-o-exclamation-triangle',
                'text' => 'Error'
            ];
        }

        if (!$validation['allowed']) {
            return [
                'color' => 'danger',
                'icon' => 'heroicon-o-map-pin',
                'text' => 'Di luar area'
            ];
        }

        return [
            'color' => 'success',
            'icon' => 'heroicon-o-check-circle',
            'text' => 'Dalam area'
        ];
    }

    /**
     * Generate Google Maps URL for coordinates
     *
     * @param float $lat
     * @param float $lon
     * @return string
     */
    public static function getGoogleMapsUrl(float $lat, float $lon): string
    {
        return "https://www.google.com/maps?q={$lat},{$lon}";
    }

    /**
     * Validate coordinates format
     *
     * @param mixed $lat
     * @param mixed $lon
     * @return bool
     */
    public static function validateCoordinates($lat, $lon): bool
    {
        return is_numeric($lat) && is_numeric($lon) &&
            $lat >= -90 && $lat <= 90 &&
            $lon >= -180 && $lon <= 180;
    }
}

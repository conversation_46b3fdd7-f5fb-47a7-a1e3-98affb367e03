<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Schedule;
use App\Models\JadwalMasal;
use App\Models\Karyawan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Command;

class FillJadwalEntitasSeeder extends Seeder
{
    /**
     * Command instance untuk output
     */
    protected $command;

    /**
     * Set command instance
     */
    public function setCommand($command): void
    {
        $this->command = $command;
    }
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Memulai pengisian entitas_id untuk jadwal...');

        // Tampilkan statistik awal
        $this->showInitialStatistics();

        // Fill entitas_id untuk Schedule (jadwal kerja)
        $this->fillScheduleEntitas();

        // Fill entitas_id untuk JadwalMasal
        $this->fillJadwalMasalEntitas();

        // Tampilkan statistik akhir
        $this->showFinalStatistics();

        $this->command->info('✅ Selesai mengisi entitas_id untuk semua jadwal!');
    }

    /**
     * Fill entitas_id untuk Schedule berdasarkan entitas_id karyawan
     */
    private function fillScheduleEntitas(): void
    {
        $this->command->info('📅 Mengisi entitas_id untuk Schedule (jadwal kerja)...');

        // Ambil semua schedule yang entitas_id-nya null
        $schedulesWithoutEntitas = Schedule::whereNull('entitas_id')
            ->with('karyawan')
            ->get();

        $this->command->info("📊 Ditemukan {$schedulesWithoutEntitas->count()} schedule tanpa entitas_id");

        $updated = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($schedulesWithoutEntitas as $schedule) {
            try {
                if ($schedule->karyawan && $schedule->karyawan->entitas_id) {
                    // Update entitas_id berdasarkan entitas_id karyawan
                    $schedule->update([
                        'entitas_id' => $schedule->karyawan->entitas_id
                    ]);
                    $updated++;

                    if ($updated % 100 == 0) {
                        $this->command->info("   ⏳ Progress: {$updated} schedule telah diupdate...");
                    }
                } else {
                    $skipped++;
                    Log::warning("Schedule ID {$schedule->id}: Karyawan tidak memiliki entitas_id", [
                        'schedule_id' => $schedule->id,
                        'karyawan_id' => $schedule->karyawan_id,
                        'karyawan_name' => $schedule->karyawan?->nama_lengkap ?? 'Unknown'
                    ]);
                }
            } catch (\Exception $e) {
                $errors++;
                Log::error("Error updating Schedule ID {$schedule->id}: " . $e->getMessage(), [
                    'schedule_id' => $schedule->id,
                    'karyawan_id' => $schedule->karyawan_id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->command->info("   ✅ Schedule: {$updated} berhasil diupdate, {$skipped} dilewati, {$errors} error");
    }

    /**
     * Fill entitas_id untuk JadwalMasal berdasarkan entitas_id karyawan
     */
    private function fillJadwalMasalEntitas(): void
    {
        $this->command->info('📋 Mengisi entitas_id untuk JadwalMasal...');

        // Ambil semua jadwal masal yang entitas_id-nya null
        $jadwalMasalWithoutEntitas = JadwalMasal::whereNull('entitas_id')
            ->with('karyawan')
            ->get();

        $this->command->info("📊 Ditemukan {$jadwalMasalWithoutEntitas->count()} jadwal masal tanpa entitas_id");

        $updated = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($jadwalMasalWithoutEntitas as $jadwalMasal) {
            try {
                if ($jadwalMasal->karyawan && $jadwalMasal->karyawan->entitas_id) {
                    // Update entitas_id berdasarkan entitas_id karyawan
                    $jadwalMasal->update([
                        'entitas_id' => $jadwalMasal->karyawan->entitas_id
                    ]);
                    $updated++;

                    if ($updated % 100 == 0) {
                        $this->command->info("   ⏳ Progress: {$updated} jadwal masal telah diupdate...");
                    }
                } else {
                    $skipped++;
                    Log::warning("JadwalMasal ID {$jadwalMasal->id}: Karyawan tidak memiliki entitas_id", [
                        'jadwal_masal_id' => $jadwalMasal->id,
                        'karyawan_id' => $jadwalMasal->karyawan_id,
                        'karyawan_name' => $jadwalMasal->karyawan?->nama_lengkap ?? 'Unknown'
                    ]);
                }
            } catch (\Exception $e) {
                $errors++;
                Log::error("Error updating JadwalMasal ID {$jadwalMasal->id}: " . $e->getMessage(), [
                    'jadwal_masal_id' => $jadwalMasal->id,
                    'karyawan_id' => $jadwalMasal->karyawan_id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->command->info("   ✅ JadwalMasal: {$updated} berhasil diupdate, {$skipped} dilewati, {$errors} error");
    }

    /**
     * Tampilkan statistik awal sebelum update
     */
    private function showInitialStatistics(): void
    {
        $this->command->info('📊 Statistik Awal:');

        // Schedule statistics
        $totalSchedules = Schedule::count();
        $schedulesWithoutEntitas = Schedule::whereNull('entitas_id')->count();

        // JadwalMasal statistics
        $totalJadwalMasal = JadwalMasal::count();
        $jadwalMasalWithoutEntitas = JadwalMasal::whereNull('entitas_id')->count();

        $this->command->table(
            ['Tabel', 'Total Records', 'Tanpa Entitas', 'Perlu Diupdate'],
            [
                ['Schedule', $totalSchedules, $schedulesWithoutEntitas, $schedulesWithoutEntitas],
                ['JadwalMasal', $totalJadwalMasal, $jadwalMasalWithoutEntitas, $jadwalMasalWithoutEntitas]
            ]
        );

        $this->command->newLine();
    }

    /**
     * Tampilkan statistik akhir setelah update
     */
    private function showFinalStatistics(): void
    {
        $this->command->info('📈 Statistik Final:');

        // Schedule statistics
        $totalSchedules = Schedule::count();
        $schedulesWithEntitas = Schedule::whereNotNull('entitas_id')->count();
        $schedulesWithoutEntitas = Schedule::whereNull('entitas_id')->count();

        $this->command->table(
            ['Tabel', 'Total', 'Dengan Entitas', 'Tanpa Entitas', 'Persentase Terisi'],
            [
                [
                    'Schedule',
                    $totalSchedules,
                    $schedulesWithEntitas,
                    $schedulesWithoutEntitas,
                    $totalSchedules > 0 ? round(($schedulesWithEntitas / $totalSchedules) * 100, 2) . '%' : '0%'
                ]
            ]
        );

        // JadwalMasal statistics
        $totalJadwalMasal = JadwalMasal::count();
        $jadwalMasalWithEntitas = JadwalMasal::whereNotNull('entitas_id')->count();
        $jadwalMasalWithoutEntitas = JadwalMasal::whereNull('entitas_id')->count();

        $this->command->table(
            ['Tabel', 'Total', 'Dengan Entitas', 'Tanpa Entitas', 'Persentase Terisi'],
            [
                [
                    'JadwalMasal',
                    $totalJadwalMasal,
                    $jadwalMasalWithEntitas,
                    $jadwalMasalWithoutEntitas,
                    $totalJadwalMasal > 0 ? round(($jadwalMasalWithEntitas / $totalJadwalMasal) * 100, 2) . '%' : '0%'
                ]
            ]
        );
    }
}

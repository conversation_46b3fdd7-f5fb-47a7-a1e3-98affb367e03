# Sistem Batas Cuti Berdasarkan Jenis Kontrak

## Overview

Sistem ini mengatur batas cuti karyawan berdasarkan jenis kontrak mereka dengan aturan sebagai berikut:

### Aturan Batas Cuti

| <PERSON><PERSON> | Batas Cuti Tahunan | Batas Cuti Bulanan        | Keterangan                   |
| ------------- | ------------------ | ------------------------- | ---------------------------- |
| **PKWT**      | 12 hari/tahun      | 3 hari/bulan (notifikasi) | Kontrak waktu tertentu       |
| **PKWTT**     | 12 hari/tahun      | -                         | Kontrak waktu tidak tertentu |
| **Freelance** | Tidak ada          | -                         | Tidak memiliki hak cuti      |
| **Probation** | Tidak ada          | -                         | Tidak memiliki hak cuti      |

### Catatan Penting

-   **Batas bulanan untuk PKWT**: <PERSON><PERSON> berupa per<PERSON>/notifikasi, tidak memblokir pengajuan cuti
-   **<PERSON><PERSON>**: Memblokir pengajuan cuti jika kuota sudah habis
-   **Freelance & Probation**: Tidak dapat mengajukan cuti, hanya bisa izin atau sakit

## Implementasi

### 1. Model CutiIzin

File: `app/Models/CutiIzin.php`

#### Method Utama:

-   `validateLeaveQuota()`: Validasi kuota cuti berdasarkan jenis kontrak
-   `getRemainingLeaveQuota($karyawanId)`: Menghitung sisa kuota cuti tahunan
-   `validateDates()`: Validasi tanggal dan kuota (dipanggil otomatis)

### 2. Form Validation

#### Karyawan Panel

File: `app/Filament/Karyawan/Resources/CutiIzinResource.php`

-   Helper text dinamis menampilkan sisa kuota
-   Peringatan untuk jenis kontrak tanpa hak cuti

#### Admin Panel

File: `app/Filament/Resources/KaryawanResource/RelationManagers/CutiIzinRelationManager.php`

-   Helper text untuk admin saat mengelola cuti karyawan

### 3. Widget Dashboard

File: `app/Filament/Karyawan/Widgets/CutiQuotaWidget.php`

-   Menampilkan informasi kuota cuti di dashboard karyawan
-   Statistik sisa kuota, cuti terpakai, dan jenis kontrak
-   Khusus PKWT: menampilkan kuota bulanan

### 4. Validasi di Pages

#### Create Page

File: `app/Filament/Karyawan/Resources/CutiIzinResource/Pages/CreateCutiIzin.php`

-   Validasi sebelum menyimpan data
-   Peringatan untuk batas bulanan (tidak memblokir)
-   Error untuk batas tahunan (memblokir)

#### Edit Page

File: `app/Filament/Karyawan/Resources/CutiIzinResource/Pages/EditCutiIzin.php`

-   Validasi yang sama saat mengedit permohonan cuti

## Cara Kerja Validasi

### 1. Pengecekan Jenis Kontrak

```php
$activeContract = $karyawan->riwayatKontrak()
    ->where('is_active', 1)
    ->orderBy('tgl_mulai', 'desc')
    ->first();

$contractType = $activeContract->jenis_kontrak;
```

### 2. Validasi Berdasarkan Kontrak

```php
// Freelance & Probation: Tidak ada hak cuti
if (in_array($contractType, ['Freelance', 'Probation'])) {
    return ['error' => 'Tidak memiliki hak cuti'];
}

// PKWT & PKWTT: 12 hari per tahun
$yearlyQuota = 12;
```

### 3. Perhitungan Kuota Terpakai

```php
$usedDaysThisYear = CutiIzin::where('karyawan_id', $karyawanId)
    ->where('jenis_permohonan', 'cuti')
    ->where('status', 'approved')
    ->whereYear('tanggal_mulai', $currentYear)
    ->sum('jumlah_hari');
```

### 4. Validasi Batas Tahunan

```php
if (($usedDaysThisYear + $requestedDays) > $yearlyQuota) {
    // Blokir pengajuan
    return ['error' => 'Kuota cuti tahunan tidak mencukupi'];
}
```

### 5. Peringatan Batas Bulanan (PKWT)

```php
if ($contractType === 'PKWT') {
    $monthlyQuota = 3;
    if (($usedDaysThisMonth + $requestedDays) > $monthlyQuota) {
        // Hanya peringatan, tidak memblokir
        return ['warning' => 'Batas cuti bulanan terlampaui'];
    }
}
```

## User Experience

### Dashboard Karyawan

-   Widget menampilkan:
    -   Jenis kontrak
    -   Sisa kuota cuti tahunan
    -   Cuti yang sudah terpakai
    -   Kuota bulanan (khusus PKWT)

### Form Pengajuan Cuti - FITUR BARU! 🎉

#### 1. Informasi Kuota Cuti (Section Atas)

-   **Status Kontrak**: Menampilkan jenis kontrak karyawan
-   **Indikator Visual**:
    -   🟢 Hijau: Kuota aman (>6 hari)
    -   🟡 Kuning: Kuota terbatas (3-6 hari)
    -   🔴 Merah: Kuota hampir habis (<3 hari)
-   **Informasi Lengkap**:
    -   ✅ Status: BOLEH/TIDAK BOLEH mengajukan cuti
    -   📊 Sisa kuota tahunan (dari 12 hari)
    -   📈 Cuti yang sudah terpakai
    -   📅 Kuota bulanan untuk PKWT (3 hari/bulan)

#### 2. Simulasi Penggunaan Kuota Real-time

-   **Update Otomatis**: Saat user memilih tanggal, sistem langsung menghitung:
    -   Jumlah hari kerja yang akan digunakan
    -   Sisa kuota setelah pengajuan cuti
    -   Status apakah pengajuan dapat disetujui
-   **Indikator Visual**:
    -   ✅ DAPAT DIAJUKAN (hijau)
    -   ❌ MELEBIHI KUOTA (merah)

#### 3. Peringatan Khusus Kontrak

-   **Freelance/Probation**: Peringatan kuning bahwa tidak memiliki hak cuti
-   **PKWT**: Informasi tambahan tentang batas bulanan 3 hari
-   **Kontrak Tidak Aktif**: Peringatan merah untuk menghubungi HRD

### Form Admin Panel - FITUR BARU! 🎉

#### 1. Informasi Kuota Karyawan (Setelah Pilih Karyawan)

-   **Pemilihan Karyawan**: Dropdown dengan filter berdasarkan role admin
-   **Informasi Otomatis**: Setelah pilih karyawan, langsung tampil:
    -   Status kontrak karyawan yang dipilih
    -   Sisa kuota cuti tahunan
    -   Cuti yang sudah terpakai
    -   Status BOLEH/TIDAK BOLEH mengajukan cuti
    -   Kuota bulanan untuk PKWT

#### 2. Simulasi Real-time untuk Admin

-   **Update Otomatis**: Saat admin input tanggal untuk karyawan
-   **Informasi Lengkap**:
    -   Nama karyawan yang dipilih
    -   Jumlah hari yang akan digunakan
    -   Sisa kuota setelah pengajuan
    -   Status DAPAT/TIDAK DAPAT diajukan
-   **Membantu Keputusan**: Admin dapat melihat dampak sebelum menyetujui

#### 3. Validasi Otomatis Admin Panel

-   **Create Page**: Validasi saat admin membuat cuti untuk karyawan
-   **Edit Page**: Validasi saat admin mengedit permohonan cuti
-   **Notifikasi**: Peringatan kuning untuk batas bulanan, error merah untuk kuota habis

### Notifikasi

-   **Success**: Pengajuan berhasil
-   **Warning**: Batas bulanan terlampaui (PKWT) - tidak memblokir
-   **Error**: Kuota tahunan habis atau kontrak tidak memiliki hak cuti - memblokir pengajuan

## Testing

File: `tests/Feature/CutiQuotaValidationTest.php`

### Test Cases:

1. PKWT dapat mengambil cuti dalam batas kuota
2. PKWT tidak dapat melebihi kuota tahunan
3. Freelance tidak dapat mengambil cuti
4. Probation tidak dapat mengambil cuti
5. PKWT mendapat peringatan batas bulanan
6. Perhitungan sisa kuota benar

### Menjalankan Test:

```bash
php artisan test tests/Feature/CutiQuotaValidationTest.php
```

## Maintenance

### Menambah Jenis Kontrak Baru

1. Update validasi di `CutiIzin::validateLeaveQuota()`
2. Update helper text di form resources
3. Update widget dashboard
4. Tambah test case baru

### Mengubah Batas Kuota

1. Update konstanta di `CutiIzin::validateLeaveQuota()`
2. Update dokumentasi
3. Update test cases

### Monitoring

-   Dashboard admin menampilkan statistik penggunaan cuti
-   Filter karyawan berdasarkan jenis kontrak
-   Laporan cuti per periode

# Sistem Batas Cuti Berdasarkan Jenis Kontrak

## Overview
Sistem ini mengatur batas cuti karyawan berdasarkan jenis kontrak mereka dengan aturan sebagai berikut:

### Aturan Batas Cuti

| <PERSON><PERSON> | Batas Cuti Tahunan | Batas Cuti Bulanan | Keterangan |
|---------------|-------------------|-------------------|------------|
| **PKWT** | 12 hari/tahun | 3 hari/bulan (notifikasi) | Kontrak waktu tertentu |
| **PKWTT** | 12 hari/tahun | - | Kontrak waktu tidak tertentu |
| **Freelance** | Tidak ada | - | Tidak memiliki hak cuti |
| **Probation** | Tidak ada | - | Tidak memiliki hak cuti |

### Catatan Penting
- **Batas bulanan untuk PKWT**: <PERSON><PERSON> berupa peringatan/notifikasi, tidak memblokir pengajuan cuti
- **<PERSON><PERSON>**: Memblokir pengajuan cuti jika kuota sudah habis
- **Freelance & Probation**: Tidak dapat mengajukan cuti, hanya bisa izin atau sakit

## Implementasi

### 1. Model CutiIzin
File: `app/Models/CutiIzin.php`

#### Method Utama:
- `validateLeaveQuota()`: Validasi kuota cuti berdasarkan jenis kontrak
- `getRemainingLeaveQuota($karyawanId)`: Menghitung sisa kuota cuti tahunan
- `validateDates()`: Validasi tanggal dan kuota (dipanggil otomatis)

### 2. Form Validation
#### Karyawan Panel
File: `app/Filament/Karyawan/Resources/CutiIzinResource.php`
- Helper text dinamis menampilkan sisa kuota
- Peringatan untuk jenis kontrak tanpa hak cuti

#### Admin Panel
File: `app/Filament/Resources/KaryawanResource/RelationManagers/CutiIzinRelationManager.php`
- Helper text untuk admin saat mengelola cuti karyawan

### 3. Widget Dashboard
File: `app/Filament/Karyawan/Widgets/CutiQuotaWidget.php`
- Menampilkan informasi kuota cuti di dashboard karyawan
- Statistik sisa kuota, cuti terpakai, dan jenis kontrak
- Khusus PKWT: menampilkan kuota bulanan

### 4. Validasi di Pages
#### Create Page
File: `app/Filament/Karyawan/Resources/CutiIzinResource/Pages/CreateCutiIzin.php`
- Validasi sebelum menyimpan data
- Peringatan untuk batas bulanan (tidak memblokir)
- Error untuk batas tahunan (memblokir)

#### Edit Page
File: `app/Filament/Karyawan/Resources/CutiIzinResource/Pages/EditCutiIzin.php`
- Validasi yang sama saat mengedit permohonan cuti

## Cara Kerja Validasi

### 1. Pengecekan Jenis Kontrak
```php
$activeContract = $karyawan->riwayatKontrak()
    ->where('is_active', 1)
    ->orderBy('tgl_mulai', 'desc')
    ->first();

$contractType = $activeContract->jenis_kontrak;
```

### 2. Validasi Berdasarkan Kontrak
```php
// Freelance & Probation: Tidak ada hak cuti
if (in_array($contractType, ['Freelance', 'Probation'])) {
    return ['error' => 'Tidak memiliki hak cuti'];
}

// PKWT & PKWTT: 12 hari per tahun
$yearlyQuota = 12;
```

### 3. Perhitungan Kuota Terpakai
```php
$usedDaysThisYear = CutiIzin::where('karyawan_id', $karyawanId)
    ->where('jenis_permohonan', 'cuti')
    ->where('status', 'approved')
    ->whereYear('tanggal_mulai', $currentYear)
    ->sum('jumlah_hari');
```

### 4. Validasi Batas Tahunan
```php
if (($usedDaysThisYear + $requestedDays) > $yearlyQuota) {
    // Blokir pengajuan
    return ['error' => 'Kuota cuti tahunan tidak mencukupi'];
}
```

### 5. Peringatan Batas Bulanan (PKWT)
```php
if ($contractType === 'PKWT') {
    $monthlyQuota = 3;
    if (($usedDaysThisMonth + $requestedDays) > $monthlyQuota) {
        // Hanya peringatan, tidak memblokir
        return ['warning' => 'Batas cuti bulanan terlampaui'];
    }
}
```

## User Experience

### Dashboard Karyawan
- Widget menampilkan:
  - Jenis kontrak
  - Sisa kuota cuti tahunan
  - Cuti yang sudah terpakai
  - Kuota bulanan (khusus PKWT)

### Form Pengajuan Cuti
- Helper text dinamis menampilkan sisa kuota
- Peringatan untuk kontrak tanpa hak cuti
- Validasi real-time saat submit

### Notifikasi
- **Success**: Pengajuan berhasil
- **Warning**: Batas bulanan terlampaui (PKWT)
- **Error**: Kuota tahunan habis atau kontrak tidak memiliki hak cuti

## Testing
File: `tests/Feature/CutiQuotaValidationTest.php`

### Test Cases:
1. PKWT dapat mengambil cuti dalam batas kuota
2. PKWT tidak dapat melebihi kuota tahunan
3. Freelance tidak dapat mengambil cuti
4. Probation tidak dapat mengambil cuti
5. PKWT mendapat peringatan batas bulanan
6. Perhitungan sisa kuota benar

### Menjalankan Test:
```bash
php artisan test tests/Feature/CutiQuotaValidationTest.php
```

## Maintenance

### Menambah Jenis Kontrak Baru
1. Update validasi di `CutiIzin::validateLeaveQuota()`
2. Update helper text di form resources
3. Update widget dashboard
4. Tambah test case baru

### Mengubah Batas Kuota
1. Update konstanta di `CutiIzin::validateLeaveQuota()`
2. Update dokumentasi
3. Update test cases

### Monitoring
- Dashboard admin menampilkan statistik penggunaan cuti
- Filter karyawan berdasarkan jenis kontrak
- Laporan cuti per periode

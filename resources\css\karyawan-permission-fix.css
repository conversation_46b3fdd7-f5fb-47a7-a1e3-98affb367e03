/* Fix untuk bentrok ID pada form permission karyawan */

/* Pastikan setiap section scope memiliki konteks yang unik */
[data-field-name="scope_values"] {
    position: relative;
}

/* Tambahkan namespace untuk setiap jenis scope berdasarkan label */
[data-field-name="scope_values"]:has(label:contains("Entitas")) {
    --scope-type: 'entitas';
}

[data-field-name="scope_values"]:has(label:contains("Departemen")) {
    --scope-type: 'departemen';
}

[data-field-name="scope_values"]:has(label:contains("Divisi")) {
    --scope-type: 'divisi';
}

[data-field-name="scope_values"]:has(label:contains("Karyawan")) {
    --scope-type: 'custom';
}

/* Pastikan input dalam scope yang tidak visible tidak mengganggu */
[data-field-name="scope_values"][style*="display: none"] input,
[data-field-name="scope_values"][style*="display: none"] select {
    pointer-events: none;
    visibility: hidden;
}

/* Styling untuk field yang aktif */
[data-field-name="scope_values"]:not([style*="display: none"]) {
    z-index: 1;
}

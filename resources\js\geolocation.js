// Geolocation handler for attendance system
document.addEventListener('DOMContentLoaded', function () {
    // Check if we're on the attendance create page
    if (document.querySelector('form[wire\\:submit\\.prevent="create"]') &&
        window.location.href.includes('/karyawan/absensis/create')) {
        initGeolocation();
    }
});

function initGeolocation() {
    // Create status element if it doesn't exist
    let statusElement = document.getElementById('geolocation-status');
    if (!statusElement) {
        statusElement = document.createElement('div');
        statusElement.id = 'geolocation-status';
        statusElement.className = 'px-3 py-2 text-sm rounded-lg fi-ta-text bg-warning-100 text-warning-700 dark:bg-warning-500/20 dark:text-warning-400';
        statusElement.innerHTML = 'Mengambil lokasi Anda...';

        // Find the form
        const form = document.querySelector('form[wire\\:submit\\.prevent="create"]');
        if (form) {
            // Insert status element at the top of the form
            form.insertBefore(statusElement, form.firstChild);
        }
    }

    // Check if geolocation is supported
    if (!navigator.geolocation) {
        statusElement.className = 'px-3 py-2 text-sm rounded-lg fi-ta-text bg-danger-100 text-danger-700 dark:bg-danger-500/20 dark:text-danger-400';
        statusElement.innerHTML = '<span class="font-medium">Error:</span> Geolokasi tidak didukung oleh browser Anda.';
        return;
    }

    // Try to get location
    navigator.geolocation.getCurrentPosition(
        // Success callback
        function (position) {
            // Store coordinates globally for camera script
            window.currentCoordinates = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                accuracy: position.coords.accuracy,
                isValid: true
            };

            console.log("🌍 Geolocation script - GPS coordinates stored:", window.currentCoordinates);

            // Find latitude and longitude inputs
            const latitudeInput = document.querySelector('input[name="latitude"]');
            const longitudeInput = document.querySelector('input[name="longitude"]');

            if (latitudeInput && longitudeInput) {
                // Set values
                latitudeInput.value = position.coords.latitude;
                longitudeInput.value = position.coords.longitude;

                // Update status with accuracy info
                statusElement.className = 'px-3 py-2 text-sm rounded-lg fi-ta-text bg-success-100 text-success-700 dark:bg-success-500/20 dark:text-success-400';
                statusElement.innerHTML = `<span class="font-medium">Sukses:</span> Lokasi berhasil dideteksi. Akurasi: ${Math.round(position.coords.accuracy)}m`;

                // Dispatch change events to notify Livewire
                latitudeInput.dispatchEvent(new Event('input', { bubbles: true }));
                longitudeInput.dispatchEvent(new Event('input', { bubbles: true }));
            } else {
                statusElement.className = 'px-3 py-2 text-sm rounded-lg fi-ta-text bg-danger-100 text-danger-700 dark:bg-danger-500/20 dark:text-danger-400';
                statusElement.innerHTML = '<span class="font-medium">Error:</span> Input lokasi tidak ditemukan.';
            }
        },
        // Error callback
        function (error) {
            // Mark coordinates as invalid
            window.currentCoordinates = {
                latitude: null,
                longitude: null,
                accuracy: null,
                isValid: false,
                error: error.code
            };

            console.log("🌍 Geolocation script - GPS error:", window.currentCoordinates);

            statusElement.className = 'px-3 py-2 text-sm rounded-lg fi-ta-text bg-danger-100 text-danger-700 dark:bg-danger-500/20 dark:text-danger-400';

            switch (error.code) {
                case error.PERMISSION_DENIED:
                    statusElement.innerHTML = '<span class="font-medium">Error:</span> Izin akses lokasi ditolak. Silakan aktifkan izin lokasi di pengaturan browser Anda.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    statusElement.innerHTML = '<span class="font-medium">Error:</span> Informasi lokasi tidak tersedia.';
                    break;
                case error.TIMEOUT:
                    statusElement.innerHTML = '<span class="font-medium">Error:</span> Waktu permintaan lokasi habis.';
                    break;
                case error.UNKNOWN_ERROR:
                    statusElement.innerHTML = '<span class="font-medium">Error:</span> Terjadi kesalahan yang tidak diketahui.';
                    break;
            }
        },
        // Options
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0
        }
    );
}

// Fix untuk bentrok ID pada form permission karyawan
document.addEventListener('DOMContentLoaded', function() {
    function fixScopeFieldIds() {
        // Cari semua field scope_values
        const scopeFields = document.querySelectorAll('[data-field-name="scope_values"]');
        
        scopeFields.forEach((field, index) => {
            // Skip jika field tidak visible
            if (field.style.display === 'none' || field.offsetParent === null) {
                return;
            }
            
            // Tentukan jenis scope berdasarkan label
            const label = field.querySelector('label');
            let scopeType = 'unknown';
            
            if (label) {
                const labelText = label.textContent.toLowerCase();
                if (labelText.includes('entitas')) {
                    scopeType = 'entitas';
                } else if (labelText.includes('departemen')) {
                    scopeType = 'departemen';
                } else if (labelText.includes('divisi')) {
                    scopeType = 'divisi';
                } else if (labelText.includes('karyawan')) {
                    scopeType = 'custom';
                }
            }
            
            // Tambahkan atribut untuk identifikasi
            field.setAttribute('data-scope-type', scopeType);
            
            // Update ID untuk semua input dalam field ini
            const inputs = field.querySelectorAll('input, select');
            inputs.forEach((input, inputIndex) => {
                if (input.id) {
                    // Buat ID yang unik berdasarkan scope type dan index
                    const newId = `${input.id}_${scopeType}_${inputIndex}`;
                    
                    // Update ID input
                    input.id = newId;
                    
                    // Update label yang terkait
                    const relatedLabel = field.querySelector(`label[for="${input.id}"]`);
                    if (relatedLabel) {
                        relatedLabel.setAttribute('for', newId);
                    }
                }
            });
        });
    }
    
    // Jalankan fix saat halaman dimuat
    fixScopeFieldIds();
    
    // Jalankan fix setelah Livewire update
    document.addEventListener('livewire:navigated', fixScopeFieldIds);
    
    // Untuk Livewire v3
    if (typeof Livewire !== 'undefined') {
        Livewire.hook('morph.updated', () => {
            setTimeout(fixScopeFieldIds, 100);
        });
    }
    
    // Observer untuk mendeteksi perubahan DOM
    const observer = new MutationObserver(function(mutations) {
        let shouldFix = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' || mutation.type === 'attributes') {
                // Cek apakah ada perubahan pada field scope_values
                const target = mutation.target;
                if (target.querySelector && target.querySelector('[data-field-name="scope_values"]')) {
                    shouldFix = true;
                }
            }
        });
        
        if (shouldFix) {
            setTimeout(fixScopeFieldIds, 50);
        }
    });
    
    // Mulai observasi
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
    });
});

// Fungsi untuk clear field saat scope type berubah
function clearScopeValues() {
    const scopeFields = document.querySelectorAll('[data-field-name="scope_values"]');
    
    scopeFields.forEach(field => {
        // Clear checkboxes
        const checkboxes = field.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
        });
        
        // Clear selects
        const selects = field.querySelectorAll('select');
        selects.forEach(select => {
            select.value = '';
            Array.from(select.options).forEach(option => option.selected = false);
            select.dispatchEvent(new Event('change', { bubbles: true }));
        });
    });
}

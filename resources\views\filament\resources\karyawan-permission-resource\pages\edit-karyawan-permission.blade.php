<x-filament-panels::page>
    <style>
        /* Fix untuk bentrok ID pada form permission karyawan */
        [data-field-name="scope_values"] {
            position: relative;
        }
        
        /* Tambahkan namespace untuk setiap jenis scope berdasarkan label */
        [data-field-name="scope_values"]:has(label:contains("Entitas")) {
            --scope-type: 'entitas';
        }
        
        [data-field-name="scope_values"]:has(label:contains("Departemen")) {
            --scope-type: 'departemen';
        }
        
        [data-field-name="scope_values"]:has(label:contains("Divisi")) {
            --scope-type: 'divisi';
        }
        
        [data-field-name="scope_values"]:has(label:contains("Karyawan")) {
            --scope-type: 'custom';
        }
        
        /* Pastikan input dalam scope yang tidak visible tidak mengganggu */
        [data-field-name="scope_values"][style*="display: none"] input,
        [data-field-name="scope_values"][style*="display: none"] select {
            pointer-events: none;
            visibility: hidden;
        }
    </style>
    
    {{ $this->form }}
    
    <script>
        // Fix untuk bentrok ID pada form permission karyawan
        document.addEventListener('DOMContentLoaded', function() {
            // Fungsi untuk fix ID yang bentrok
            function fixScopeFieldIds() {
                // Cari semua field scope_values
                const scopeFields = document.querySelectorAll('[data-field-name="scope_values"]');
                
                // Tambahkan atribut data-scope-type berdasarkan label
                scopeFields.forEach((field, index) => {
                    const label = field.querySelector('label');
                    if (label) {
                        const labelText = label.textContent.toLowerCase();
                        if (labelText.includes('entitas')) {
                            field.setAttribute('data-scope-type', 'entitas');
                        } else if (labelText.includes('departemen')) {
                            field.setAttribute('data-scope-type', 'departemen');
                        } else if (labelText.includes('divisi')) {
                            field.setAttribute('data-scope-type', 'divisi');
                        } else if (labelText.includes('karyawan')) {
                            field.setAttribute('data-scope-type', 'custom');
                        }
                    }
                    
                    // Tambahkan ID unik untuk setiap input
                    const inputs = field.querySelectorAll('input, select');
                    inputs.forEach((input, inputIndex) => {
                        if (input.id) {
                            const scopeType = field.getAttribute('data-scope-type') || 'unknown';
                            input.id = `${input.id}_${scopeType}_${index}_${inputIndex}`;
                        }
                    });
                });
            }
            
            // Jalankan fix saat halaman dimuat
            fixScopeFieldIds();
            
            // Tambahkan event listener untuk scope_type
            const scopeTypeSelect = document.querySelector('[data-field-name="scope_type"] select');
            if (scopeTypeSelect) {
                scopeTypeSelect.addEventListener('change', function() {
                    // Beri waktu untuk Livewire update DOM
                    setTimeout(fixScopeFieldIds, 100);
                    
                    // Clear semua field scope_values
                    const scopeFields = document.querySelectorAll('[data-field-name="scope_values"]');
                    scopeFields.forEach(field => {
                        // Clear checkboxes
                        const checkboxes = field.querySelectorAll('input[type="checkbox"]');
                        checkboxes.forEach(checkbox => {
                            checkbox.checked = false;
                            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                        });
                        
                        // Clear selects
                        const selects = field.querySelectorAll('select');
                        selects.forEach(select => {
                            select.value = '';
                            Array.from(select.options).forEach(option => option.selected = false);
                            select.dispatchEvent(new Event('change', { bubbles: true }));
                        });
                    });
                });
            }
        });
    </script>
</x-filament-panels::page>

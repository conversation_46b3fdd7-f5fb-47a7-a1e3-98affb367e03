<x-filament-panels::page>
    <style>
        /* Ensure unique styling for scope value fields */
        [data-field-name="scope_values"] {
            position: relative;
        }

        /* Add unique identifiers to prevent conflicts */
        [data-field-name="scope_values"][data-scope-type="entitas"] {
            --scope-identifier: 'entitas';
        }

        [data-field-name="scope_values"][data-scope-type="departemen"] {
            --scope-identifier: 'departemen';
        }

        [data-field-name="scope_values"][data-scope-type="divisi"] {
            --scope-identifier: 'divisi';
        }

        [data-field-name="scope_values"][data-scope-type="custom"] {
            --scope-identifier: 'custom';
        }
    </style>

    <div x-data="{
        scopeType: @entangle('data.scope_type'),
        scopeValues: @entangle('data.scope_values'),
    
        clearScopeValues() {
            this.scopeValues = null;
            // Clear all checkboxes and selects
            this.$nextTick(() => {
                document.querySelectorAll('[data-field-name=\'scope_values\'] input[type=\'checkbox\']').forEach(cb => cb.checked = false);
                document.querySelectorAll('[data-field-name=\'scope_values\'] select').forEach(sel => sel.value = '');
            });
        }
    }" x-watch="scopeType" x-on:scope-type-changed="clearScopeValues()">
        {{ $this->form }}
    </div>

    <script>
        // Additional JavaScript to handle form conflicts
        document.addEventListener('livewire:navigated', function() {
            // Add unique attributes to scope value fields to prevent ID conflicts
            const scopeFields = document.querySelectorAll('[data-field-name="scope_values"]');
            scopeFields.forEach((field, index) => {
                // Add unique identifier based on visible state and content
                const label = field.querySelector('label');
                if (label) {
                    const labelText = label.textContent.toLowerCase();
                    if (labelText.includes('entitas')) {
                        field.setAttribute('data-scope-type', 'entitas');
                    } else if (labelText.includes('departemen')) {
                        field.setAttribute('data-scope-type', 'departemen');
                    } else if (labelText.includes('divisi')) {
                        field.setAttribute('data-scope-type', 'divisi');
                    } else if (labelText.includes('karyawan')) {
                        field.setAttribute('data-scope-type', 'custom');
                    }
                }

                // Add unique ID suffix to prevent conflicts
                const inputs = field.querySelectorAll('input, select');
                inputs.forEach((input, inputIndex) => {
                    if (input.id) {
                        input.id = input.id + '_' + index + '_' + inputIndex;
                    }
                });
            });
        });

        // Trigger on initial load
        document.addEventListener('DOMContentLoaded', function() {
            document.dispatchEvent(new Event('livewire:navigated'));
        });
    </script>
</x-filament-panels::page>

<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <style>
        /* Fix untuk bentrok ID pada form permission karyawan */
        [data-field-name="scope_values"] {
            position: relative;
        }
        
        /* Tambahkan namespace untuk setiap jenis scope berdasarkan label */
        [data-field-name="scope_values"]:has(label:contains("Entitas")) {
            --scope-type: 'entitas';
        }
        
        [data-field-name="scope_values"]:has(label:contains("Departemen")) {
            --scope-type: 'departemen';
        }
        
        [data-field-name="scope_values"]:has(label:contains("Divisi")) {
            --scope-type: 'divisi';
        }
        
        [data-field-name="scope_values"]:has(label:contains("Karyawan")) {
            --scope-type: 'custom';
        }
        
        /* Pastikan input dalam scope yang tidak visible tidak mengganggu */
        [data-field-name="scope_values"][style*="display: none"] input,
        [data-field-name="scope_values"][style*="display: none"] select {
            pointer-events: none;
            visibility: hidden;
        }
    </style>
    
    <?php echo e($this->form); ?>

    
    <script>
        // Fix untuk bentrok ID pada form permission karyawan
        document.addEventListener('DOMContentLoaded', function() {
            // Fungsi untuk fix ID yang bentrok
            function fixScopeFieldIds() {
                // Cari semua field scope_values
                const scopeFields = document.querySelectorAll('[data-field-name="scope_values"]');
                
                // Tambahkan atribut data-scope-type berdasarkan label
                scopeFields.forEach((field, index) => {
                    const label = field.querySelector('label');
                    if (label) {
                        const labelText = label.textContent.toLowerCase();
                        if (labelText.includes('entitas')) {
                            field.setAttribute('data-scope-type', 'entitas');
                        } else if (labelText.includes('departemen')) {
                            field.setAttribute('data-scope-type', 'departemen');
                        } else if (labelText.includes('divisi')) {
                            field.setAttribute('data-scope-type', 'divisi');
                        } else if (labelText.includes('karyawan')) {
                            field.setAttribute('data-scope-type', 'custom');
                        }
                    }
                    
                    // Tambahkan ID unik untuk setiap input
                    const inputs = field.querySelectorAll('input, select');
                    inputs.forEach((input, inputIndex) => {
                        if (input.id) {
                            const scopeType = field.getAttribute('data-scope-type') || 'unknown';
                            input.id = `${input.id}_${scopeType}_${index}_${inputIndex}`;
                        }
                    });
                });
            }
            
            // Jalankan fix saat halaman dimuat
            fixScopeFieldIds();
            
            // Tambahkan event listener untuk scope_type
            const scopeTypeSelect = document.querySelector('[data-field-name="scope_type"] select');
            if (scopeTypeSelect) {
                scopeTypeSelect.addEventListener('change', function() {
                    // Beri waktu untuk Livewire update DOM
                    setTimeout(fixScopeFieldIds, 100);
                    
                    // Clear semua field scope_values
                    const scopeFields = document.querySelectorAll('[data-field-name="scope_values"]');
                    scopeFields.forEach(field => {
                        // Clear checkboxes
                        const checkboxes = field.querySelectorAll('input[type="checkbox"]');
                        checkboxes.forEach(checkbox => {
                            checkbox.checked = false;
                            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                        });
                        
                        // Clear selects
                        const selects = field.querySelectorAll('select');
                        selects.forEach(select => {
                            select.value = '';
                            Array.from(select.options).forEach(option => option.selected = false);
                            select.dispatchEvent(new Event('change', { bubbles: true }));
                        });
                    });
                });
            }
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/resources/karyawan-permission-resource/pages/create-karyawan-permission.blade.php ENDPATH**/ ?>
<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <style>
        /* Ensure unique styling for scope value fields */
        [data-field-name="scope_values"] {
            position: relative;
        }

        /* Add unique identifiers to prevent conflicts */
        [data-field-name="scope_values"][data-scope-type="entitas"] {
            --scope-identifier: 'entitas';
        }

        [data-field-name="scope_values"][data-scope-type="departemen"] {
            --scope-identifier: 'departemen';
        }

        [data-field-name="scope_values"][data-scope-type="divisi"] {
            --scope-identifier: 'divisi';
        }

        [data-field-name="scope_values"][data-scope-type="custom"] {
            --scope-identifier: 'custom';
        }
    </style>

    <div x-data="{
        scopeType: <?php if ((object) ('data.scope_type') instanceof \Livewire\WireDirective) : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('data.scope_type'->value()); ?>')<?php echo e('data.scope_type'->hasModifier('live') ? '.live' : ''); ?><?php else : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('data.scope_type'); ?>')<?php endif; ?>,
        scopeValues: <?php if ((object) ('data.scope_values') instanceof \Livewire\WireDirective) : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('data.scope_values'->value()); ?>')<?php echo e('data.scope_values'->hasModifier('live') ? '.live' : ''); ?><?php else : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('data.scope_values'); ?>')<?php endif; ?>,
    
        clearScopeValues() {
            this.scopeValues = null;
            // Clear all checkboxes and selects
            this.$nextTick(() => {
                document.querySelectorAll('[data-field-name=\'scope_values\'] input[type=\'checkbox\']').forEach(cb => cb.checked = false);
                document.querySelectorAll('[data-field-name=\'scope_values\'] select').forEach(sel => sel.value = '');
            });
        }
    }" x-watch="scopeType" x-on:scope-type-changed="clearScopeValues()">
        <?php echo e($this->form); ?>

    </div>

    <script>
        // Additional JavaScript to handle form conflicts
        document.addEventListener('livewire:navigated', function() {
            // Add unique attributes to scope value fields to prevent ID conflicts
            const scopeFields = document.querySelectorAll('[data-field-name="scope_values"]');
            scopeFields.forEach((field, index) => {
                // Add unique identifier based on visible state and content
                const label = field.querySelector('label');
                if (label) {
                    const labelText = label.textContent.toLowerCase();
                    if (labelText.includes('entitas')) {
                        field.setAttribute('data-scope-type', 'entitas');
                    } else if (labelText.includes('departemen')) {
                        field.setAttribute('data-scope-type', 'departemen');
                    } else if (labelText.includes('divisi')) {
                        field.setAttribute('data-scope-type', 'divisi');
                    } else if (labelText.includes('karyawan')) {
                        field.setAttribute('data-scope-type', 'custom');
                    }
                }

                // Add unique ID suffix to prevent conflicts
                const inputs = field.querySelectorAll('input, select');
                inputs.forEach((input, inputIndex) => {
                    if (input.id) {
                        input.id = input.id + '_' + index + '_' + inputIndex;
                    }
                });
            });
        });

        // Trigger on initial load
        document.addEventListener('DOMContentLoaded', function() {
            document.dispatchEvent(new Event('livewire:navigated'));
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/resources/karyawan-permission-resource/pages/create-karyawan-permission.blade.php ENDPATH**/ ?>
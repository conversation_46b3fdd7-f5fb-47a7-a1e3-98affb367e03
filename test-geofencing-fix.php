<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Karyawan;
use App\Models\Schedule;
use App\Models\Entitas;
use App\Services\GeofencingService;
use Carbon\Carbon;

echo "🧪 Testing Geofencing Fix\n";
echo "========================\n\n";

// Test coordinates (same as in screenshot)
$testLat = 0.471859;
$testLng = 101.443174;

echo "📍 Test Coordinates: {$testLat}, {$testLng}\n\n";

// Find a karyawan for testing
$karyawan = Karyawan::with(['entitas', 'user'])->first();

if (!$karyawan) {
    echo "❌ No karyawan found for testing\n";
    exit;
}

echo "👤 Testing with Karyawan: {$karyawan->nama} (ID: {$karyawan->id})\n";
echo "🏢 Default Entitas: " . ($karyawan->entitas ? $karyawan->entitas->nama : 'None') . "\n\n";

// Check today's schedule
$today = Carbon::today()->format('Y-m-d');
$jadwal = Schedule::where('karyawan_id', $karyawan->id)
    ->whereDate('tanggal_jadwal', $today)
    ->with('entitas')
    ->first();

echo "📅 Today's Schedule ({$today}):\n";
if ($jadwal) {
    echo "   - Schedule ID: {$jadwal->id}\n";
    echo "   - Entitas ID: " . ($jadwal->entitas_id ?? 'None') . "\n";
    echo "   - Entitas Name: " . ($jadwal->entitas ? $jadwal->entitas->nama : 'None') . "\n";
    if ($jadwal->entitas) {
        echo "   - Entitas Coordinates: {$jadwal->entitas->coordinates}\n";
        echo "   - Entitas Radius: {$jadwal->entitas->radius}m\n";
    }
} else {
    echo "   - No schedule found for today\n";
}
echo "\n";

// Test getAttendanceLocationInfo
echo "🔍 Testing getAttendanceLocationInfo():\n";
$locationInfo = GeofencingService::getAttendanceLocationInfo($karyawan);
echo "   - Has Location: " . ($locationInfo['has_location'] ? 'Yes' : 'No') . "\n";
if ($locationInfo['has_location']) {
    echo "   - Entitas Name: {$locationInfo['entitas_name']}\n";
    echo "   - Coordinates: {$locationInfo['coordinates']}\n";
    echo "   - Radius: {$locationInfo['radius']}m\n";
    echo "   - Source: {$locationInfo['source']}\n";
}
echo "\n";

// Test validateAttendanceLocation
echo "🔍 Testing validateAttendanceLocation():\n";
$validation = GeofencingService::validateAttendanceLocation($karyawan, $testLat, $testLng);
echo "   - Allowed: " . ($validation['allowed'] ? 'Yes' : 'No') . "\n";
echo "   - Distance: {$validation['distance']}m\n";
echo "   - Radius: {$validation['radius']}m\n";
echo "   - Entitas Name: {$validation['entitas_name']}\n";
echo "   - Entitas Coordinates: {$validation['entitas_coordinates']}\n";
echo "   - User Coordinates: {$validation['user_coordinates']}\n";
echo "   - Source: {$validation['source']}\n";
echo "   - Message: {$validation['message']}\n";
echo "\n";

// Compare results
echo "🔄 Comparison:\n";
if ($locationInfo['has_location']) {
    $sameEntitas = $locationInfo['entitas_name'] === $validation['entitas_name'];
    $sameSource = $locationInfo['source'] === $validation['source'];
    
    echo "   - Same Entitas: " . ($sameEntitas ? '✅ Yes' : '❌ No') . "\n";
    echo "   - Same Source: " . ($sameSource ? '✅ Yes' : '❌ No') . "\n";
    
    if ($sameEntitas && $sameSource) {
        echo "   - 🎉 Fix successful! Both functions use the same entitas.\n";
    } else {
        echo "   - ⚠️ Still inconsistent. Need further investigation.\n";
    }
}

echo "\n✅ Test completed!\n";
